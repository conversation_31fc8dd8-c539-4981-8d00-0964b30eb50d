"use client"

import { useRef, use<PERSON>emo, useCallback } from "react"
import { use<PERSON><PERSON><PERSON>, useThree } from "@react-three/fiber"
import { Points, PointMaterial } from "@react-three/drei"
import { useTheme } from "next-themes"
import * as THREE from "three"

interface InteractiveFieldProps {
  count?: number
  mouseInfluence?: number
  attractionForce?: number
}

export function InteractiveField({ count = 200, mouseInfluence = 2, attractionForce = 0.001 }: InteractiveFieldProps) {
  const ref = useRef<THREE.Points>(null)
  const { resolvedTheme } = useTheme()
  const { mouse, viewport } = useThree()
  const isDark = resolvedTheme === "dark"

  // Mouse position in 3D space
  const mousePosition = useMemo(() => new THREE.Vector3(), [])

  // Particle system with physics
  const particleSystem = useMemo(() => {
    const particles = []

    for (let i = 0; i < count; i++) {
      particles.push({
        position: new THREE.Vector3((Math.random() - 0.5) * 10, (Math.random() - 0.5) * 10, (Math.random() - 0.5) * 5),
        velocity: new THREE.Vector3(0, 0, 0),
        originalPosition: new THREE.Vector3(
          (Math.random() - 0.5) * 10,
          (Math.random() - 0.5) * 10,
          (Math.random() - 0.5) * 5,
        ),
        mass: 0.5 + Math.random() * 1.5,
        charge: Math.random() > 0.5 ? 1 : -1,
        size: 0.02 + Math.random() * 0.04,
      })
    }

    return particles
  }, [count])

  // Calculate forces between particles
  const calculateForces = useCallback(
    (particles: any[]) => {
      // Update mouse position in 3D space
      mousePosition.set((mouse.x * viewport.width) / 2, (mouse.y * viewport.height) / 2, 0)

      particles.forEach((particle, i) => {
        // Reset forces
        const totalForce = new THREE.Vector3()

        // Mouse interaction force
        const mouseDistance = particle.position.distanceTo(mousePosition)
        if (mouseDistance < mouseInfluence && mouseDistance > 0.1) {
          const mouseForce = new THREE.Vector3()
            .subVectors(mousePosition, particle.position)
            .normalize()
            .multiplyScalar(attractionForce * (mouseInfluence - mouseDistance))

          totalForce.add(mouseForce)
        }

        // Particle-to-particle forces
        particles.forEach((otherParticle, j) => {
          if (i === j) return

          const distance = particle.position.distanceTo(otherParticle.position)
          if (distance < 0.01) return // Avoid division by zero

          const force = new THREE.Vector3().subVectors(otherParticle.position, particle.position).normalize()

          // Electromagnetic-like force based on charge
          const chargeForce = (particle.charge * otherParticle.charge) / (distance * distance)

          if (isDark) {
            // Dark theme: Strong electromagnetic interactions
            force.multiplyScalar(-chargeForce * 0.0001)
          } else {
            // Light theme: Gentle gravitational-like attraction
            force.multiplyScalar(chargeForce * 0.00005)
          }

          totalForce.add(force)
        })

        // Spring force back to original position
        const springForce = new THREE.Vector3()
          .subVectors(particle.originalPosition, particle.position)
          .multiplyScalar(0.0001)

        totalForce.add(springForce)

        // Apply damping
        particle.velocity.multiplyScalar(0.95)

        // Apply force (F = ma, so a = F/m)
        const acceleration = totalForce.divideScalar(particle.mass)
        particle.velocity.add(acceleration)

        // Update position
        particle.position.add(particle.velocity)

        // Collision with invisible spheres (for visual effect)
        if (isDark) {
          const sphereCenter = new THREE.Vector3(Math.sin(Date.now() * 0.001) * 3, Math.cos(Date.now() * 0.0015) * 2, 0)
          const sphereRadius = 1.5

          const distanceToSphere = particle.position.distanceTo(sphereCenter)
          if (distanceToSphere < sphereRadius) {
            const normal = new THREE.Vector3().subVectors(particle.position, sphereCenter).normalize()

            particle.position.copy(sphereCenter.clone().add(normal.clone().multiplyScalar(sphereRadius)))
            particle.velocity.reflect(normal).multiplyScalar(0.8)
          }
        }
      })
    },
    [mouse, viewport, mousePosition, mouseInfluence, attractionForce, isDark],
  )

  useFrame(() => {
    if (!ref.current) return

    // Calculate physics
    calculateForces(particleSystem)

    // Update geometry
    const positions = ref.current.geometry.attributes.position.array as Float32Array
    const colors = ref.current.geometry.attributes.color.array as Float32Array

    particleSystem.forEach((particle, i) => {
      const i3 = i * 3

      // Update positions
      positions[i3] = particle.position.x
      positions[i3 + 1] = particle.position.y
      positions[i3 + 2] = particle.position.z

      // Update colors based on velocity
      const speed = particle.velocity.length()
      const intensity = Math.min(speed * 100, 1)

      if (isDark) {
        // Electric blue to cyan based on speed
        colors[i3] = intensity * 0.2 // R
        colors[i3 + 1] = intensity * 0.8 // G
        colors[i3 + 2] = 1.0 // B
      } else {
        // Warm orange to red based on speed
        colors[i3] = 1.0 // R
        colors[i3 + 1] = intensity * 0.5 // G
        colors[i3 + 2] = intensity * 0.2 // B
      }
    })

    ref.current.geometry.attributes.position.needsUpdate = true
    ref.current.geometry.attributes.color.needsUpdate = true
  })

  // Initial geometry setup
  const [positions, colors] = useMemo(() => {
    const positions = new Float32Array(count * 3)
    const colors = new Float32Array(count * 3)

    particleSystem.forEach((particle, i) => {
      const i3 = i * 3
      positions[i3] = particle.position.x
      positions[i3 + 1] = particle.position.y
      positions[i3 + 2] = particle.position.z

      colors[i3] = isDark ? 0.2 : 1.0
      colors[i3 + 1] = isDark ? 0.8 : 0.5
      colors[i3 + 2] = isDark ? 1.0 : 0.2
    })

    return [positions, colors]
  }, [particleSystem, count, isDark])

  return (
    <Points ref={ref} positions={positions} colors={colors}>
      <PointMaterial
        transparent
        size={isDark ? 0.05 : 0.03}
        sizeAttenuation={true}
        depthWrite={false}
        blending={isDark ? THREE.AdditiveBlending : THREE.NormalBlending}
        vertexColors
        opacity={0.8}
      />
    </Points>
  )
}
