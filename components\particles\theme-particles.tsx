"use client"

import { useRef, useMemo } from "react"
import { use<PERSON>rame } from "@react-three/fiber"
import { Points, PointMaterial } from "@react-three/drei"
import { useTheme } from "next-themes"
import * as THREE from "three"

interface ThemeParticlesProps {
  count?: number
  speed?: number
  size?: number
}

export function ThemeParticles({ count = 1000, speed = 0.5, size = 0.02 }: ThemeParticlesProps) {
  const ref = useRef<THREE.Points>(null)
  const { resolvedTheme } = useTheme()
  const isDark = resolvedTheme === "dark"

  // Generate different particle patterns based on theme
  const [positions, colors] = useMemo(() => {
    const positions = new Float32Array(count * 3)
    const colors = new Float32Array(count * 3)

    if (isDark) {
      // Dark theme: Constellation-like pattern with glowing particles
      for (let i = 0; i < count; i++) {
        const i3 = i * 3

        // Create clusters of particles (constellation effect)
        const cluster = Math.floor(Math.random() * 5)
        const clusterX = (cluster - 2) * 4
        const clusterY = (Math.random() - 0.5) * 2

        positions[i3] = clusterX + (Math.random() - 0.5) * 3
        positions[i3 + 1] = clusterY + (Math.random() - 0.5) * 3
        positions[i3 + 2] = (Math.random() - 0.5) * 8

        // Glowing colors for dark theme
        const colorVariant = Math.random()
        if (colorVariant < 0.3) {
          // Cyan glow
          colors[i3] = 0.2 + Math.random() * 0.3 // R
          colors[i3 + 1] = 0.8 + Math.random() * 0.2 // G
          colors[i3 + 2] = 1.0 // B
        } else if (colorVariant < 0.6) {
          // Purple glow
          colors[i3] = 0.6 + Math.random() * 0.4 // R
          colors[i3 + 1] = 0.2 + Math.random() * 0.3 // G
          colors[i3 + 2] = 1.0 // B
        } else {
          // Orange glow
          colors[i3] = 1.0 // R
          colors[i3 + 1] = 0.4 + Math.random() * 0.4 // G
          colors[i3 + 2] = 0.1 + Math.random() * 0.2 // B
        }
      }
    } else {
      // Light theme: Organic, flowing pattern with soft particles
      for (let i = 0; i < count; i++) {
        const i3 = i * 3

        // Create flowing, organic distribution
        const angle = Math.random() * Math.PI * 2
        const radius = Math.random() * 6
        const height = (Math.random() - 0.5) * 4

        positions[i3] = Math.cos(angle) * radius + (Math.random() - 0.5) * 2
        positions[i3 + 1] = height + Math.sin(angle * 3) * 0.5
        positions[i3 + 2] = Math.sin(angle) * radius + (Math.random() - 0.5) * 2

        // Soft, warm colors for light theme
        const colorVariant = Math.random()
        if (colorVariant < 0.4) {
          // Soft orange
          colors[i3] = 0.9 + Math.random() * 0.1 // R
          colors[i3 + 1] = 0.5 + Math.random() * 0.3 // G
          colors[i3 + 2] = 0.2 + Math.random() * 0.2 // B
        } else if (colorVariant < 0.7) {
          // Soft purple
          colors[i3] = 0.7 + Math.random() * 0.2 // R
          colors[i3 + 1] = 0.4 + Math.random() * 0.3 // G
          colors[i3 + 2] = 0.8 + Math.random() * 0.2 // B
        } else {
          // Soft blue
          colors[i3] = 0.3 + Math.random() * 0.2 // R
          colors[i3 + 1] = 0.6 + Math.random() * 0.3 // G
          colors[i3 + 2] = 0.9 + Math.random() * 0.1 // B
        }
      }
    }

    return [positions, colors]
  }, [count, isDark])

  // Different animation patterns for each theme
  useFrame((state) => {
    if (!ref.current) return

    const time = state.clock.getElapsedTime()

    if (isDark) {
      // Dark theme: Pulsing, twinkling effect
      ref.current.rotation.y = time * speed * 0.1
      ref.current.rotation.x = Math.sin(time * 0.3) * 0.1

      // Update particle opacity for twinkling effect
      const material = ref.current.material as THREE.PointsMaterial
      if (material) {
        material.opacity = 0.6 + Math.sin(time * 2) * 0.2
      }
    } else {
      // Light theme: Gentle floating motion
      ref.current.rotation.y = time * speed * 0.05
      ref.current.position.y = Math.sin(time * 0.5) * 0.2
      ref.current.position.x = Math.cos(time * 0.3) * 0.1

      // Gentle opacity variation
      const material = ref.current.material as THREE.PointsMaterial
      if (material) {
        material.opacity = 0.4 + Math.sin(time * 1.5) * 0.1
      }
    }
  })

  return (
    <Points ref={ref} positions={positions} colors={colors}>
      <PointMaterial
        transparent
        size={isDark ? size * 1.5 : size}
        sizeAttenuation={true}
        depthWrite={false}
        blending={isDark ? THREE.AdditiveBlending : THREE.NormalBlending}
        vertexColors
      />
    </Points>
  )
}
