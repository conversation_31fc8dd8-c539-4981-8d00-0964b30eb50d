"use client"

import { useEffect, useState } from "react"
import { useTheme } from "next-themes"
import { AnimatePresence, motion } from "framer-motion"
import { Sun, Moon } from "lucide-react"

export function ThemeTransition() {
  const { theme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [previousTheme, setPreviousTheme] = useState<string | undefined>(undefined)

  // Track theme changes
  useEffect(() => {
    if (!mounted) {
      setMounted(true)
      return
    }

    if (previousTheme !== undefined && previousTheme !== resolvedTheme) {
      setIsTransitioning(true)
      const timer = setTimeout(() => {
        setIsTransitioning(false)
      }, 1500) // Match the duration of the animations
      return () => clearTimeout(timer)
    }

    setPreviousTheme(resolvedTheme)
  }, [resolvedTheme, previousTheme, mounted])

  // Don't render anything until mounted to prevent hydration mismatch
  if (!mounted) return null

  const isDark = resolvedTheme === "dark"

  return (
    <>
      {/* Full-screen overlay transition effect */}
      <AnimatePresence mode="wait">
        {isTransitioning && (
          <motion.div
            key={`theme-transition-${isDark ? "dark" : "light"}`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="fixed inset-0 z-[100] pointer-events-none"
          >
            {/* Radial expanding circle */}
            <motion.div
              initial={{ scale: 0, opacity: 0.7 }}
              animate={{ scale: 2, opacity: 0 }}
              transition={{ duration: 1.2, ease: [0.22, 1, 0.36, 1] }}
              className={`absolute top-4 right-4 w-12 h-12 rounded-full ${isDark ? "bg-gray-900" : "bg-orange-50"}`}
              style={{
                transformOrigin: "center",
                boxShadow: isDark ? "0 0 40px 20px rgba(0, 0, 0, 0.3)" : "0 0 40px 20px rgba(255, 247, 237, 0.5)",
              }}
            />

            {/* Floating particles */}
            <div className="absolute inset-0 overflow-hidden">
              {Array.from({ length: 20 }).map((_, i) => (
                <motion.div
                  key={`particle-${i}`}
                  initial={{
                    x: "50vw",
                    y: "10vh",
                    scale: 0,
                    opacity: 0,
                  }}
                  animate={{
                    x: `${35 + Math.random() * 30}vw`,
                    y: `${Math.random() * 100}vh`,
                    scale: Math.random() * 0.5 + 0.5,
                    opacity: Math.random() * 0.6 + 0.4,
                  }}
                  transition={{
                    duration: Math.random() * 1 + 0.5,
                    ease: "easeOut",
                    delay: Math.random() * 0.2,
                  }}
                  className={`absolute w-2 h-2 rounded-full ${
                    isDark ? "bg-blue-400 shadow-blue-400/50" : "bg-orange-400 shadow-orange-400/50"
                  }`}
                  style={{
                    boxShadow: isDark ? "0 0 10px 2px rgba(96, 165, 250, 0.5)" : "0 0 10px 2px rgba(251, 146, 60, 0.5)",
                  }}
                />
              ))}
            </div>

            {/* Theme icon */}
            <motion.div
              initial={{ scale: 0, rotate: -180, opacity: 0 }}
              animate={{ scale: 1, rotate: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
            >
              {isDark ? (
                <Moon
                  size={64}
                  className="text-white opacity-80"
                  style={{ filter: "drop-shadow(0 0 8px rgba(255, 255, 255, 0.5))" }}
                />
              ) : (
                <Sun
                  size={64}
                  className="text-orange-500 opacity-80"
                  style={{ filter: "drop-shadow(0 0 8px rgba(249, 115, 22, 0.5))" }}
                />
              )}
            </motion.div>

            {/* Background stars/light rays */}
            <div className="absolute inset-0 overflow-hidden">
              {Array.from({ length: isDark ? 30 : 12 }).map((_, i) => (
                <motion.div
                  key={`star-${i}`}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: Math.random() * 0.5 + 0.3, scale: Math.random() * 1 + 0.5 }}
                  transition={{ duration: 0.8, delay: Math.random() * 0.4 }}
                  className="absolute"
                  style={{
                    top: `${Math.random() * 100}%`,
                    left: `${Math.random() * 100}%`,
                    width: isDark ? "2px" : "1px",
                    height: isDark ? "2px" : "80px",
                    background: isDark ? "white" : `linear-gradient(to bottom, rgba(249, 115, 22, 0.7), transparent)`,
                    transform: isDark ? "scale(1)" : `rotate(${Math.random() * 360}deg)`,
                    boxShadow: isDark ? "0 0 4px 1px rgba(255, 255, 255, 0.8)" : "none",
                  }}
                />
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Subtle page transition effect */}
      <AnimatePresence mode="wait">
        <motion.div
          key={`theme-bg-${isDark ? "dark" : "light"}`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className="fixed inset-0 pointer-events-none z-[-1]"
        >
          <div
            className={`absolute inset-0 ${
              isDark ? "bg-gradient-to-b from-gray-900 to-gray-950" : "bg-gradient-to-b from-orange-50 to-white"
            } transition-colors duration-1000`}
          />
        </motion.div>
      </AnimatePresence>
    </>
  )
}
