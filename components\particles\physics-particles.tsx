"use client"

import { useRef, useMemo, useCallback } from "react"
import { use<PERSON>rame } from "@react-three/fiber"
import { Points, PointMaterial } from "@react-three/drei"
import { useTheme } from "next-themes"
import * as THREE from "three"

interface Particle {
  position: THREE.Vector3
  velocity: THREE.Vector3
  acceleration: THREE.Vector3
  mass: number
  radius: number
  life: number
  maxLife: number
  color: THREE.Color
}

interface PhysicsParticlesProps {
  count?: number
  bounds?: number
  gravity?: number
  damping?: number
}

export function PhysicsParticles({ count = 300, bounds = 8, gravity = -0.001, damping = 0.98 }: PhysicsParticlesProps) {
  const ref = useRef<THREE.Points>(null)
  const { resolvedTheme } = useTheme()
  const isDark = resolvedTheme === "dark"

  // Initialize particles with physics properties
  const particles = useMemo(() => {
    const particleArray: Particle[] = []

    for (let i = 0; i < count; i++) {
      const particle: Particle = {
        position: new THREE.Vector3(
          (Math.random() - 0.5) * bounds,
          (Math.random() - 0.5) * bounds,
          (Math.random() - 0.5) * bounds,
        ),
        velocity: new THREE.Vector3(
          (Math.random() - 0.5) * 0.02,
          (Math.random() - 0.5) * 0.02,
          (Math.random() - 0.5) * 0.02,
        ),
        acceleration: new THREE.Vector3(0, gravity, 0),
        mass: 0.5 + Math.random() * 1.5,
        radius: 0.05 + Math.random() * 0.1,
        life: Math.random() * 100,
        maxLife: 100 + Math.random() * 200,
        color: new THREE.Color(),
      }

      // Set theme-appropriate colors
      if (isDark) {
        const colorVariant = Math.random()
        if (colorVariant < 0.33) {
          particle.color.setHSL(0.5, 0.8, 0.6) // Cyan
        } else if (colorVariant < 0.66) {
          particle.color.setHSL(0.75, 0.8, 0.6) // Purple
        } else {
          particle.color.setHSL(0.08, 0.9, 0.6) // Orange
        }
      } else {
        const colorVariant = Math.random()
        if (colorVariant < 0.33) {
          particle.color.setHSL(0.08, 0.7, 0.5) // Orange
        } else if (colorVariant < 0.66) {
          particle.color.setHSL(0.75, 0.6, 0.5) // Purple
        } else {
          particle.color.setHSL(0.55, 0.6, 0.5) // Blue
        }
      }

      particleArray.push(particle)
    }

    return particleArray
  }, [count, bounds, gravity, isDark])

  // Collision detection between particles
  const checkParticleCollisions = useCallback(
    (particles: Particle[]) => {
      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const p1 = particles[i]
          const p2 = particles[j]

          const distance = p1.position.distanceTo(p2.position)
          const minDistance = p1.radius + p2.radius

          if (distance < minDistance) {
            // Calculate collision response
            const normal = new THREE.Vector3().subVectors(p2.position, p1.position).normalize()

            // Separate particles
            const overlap = minDistance - distance
            const separation = normal.clone().multiplyScalar(overlap * 0.5)

            p1.position.sub(separation)
            p2.position.add(separation)

            // Calculate relative velocity
            const relativeVelocity = new THREE.Vector3().subVectors(p1.velocity, p2.velocity)
            const velocityAlongNormal = relativeVelocity.dot(normal)

            // Don't resolve if velocities are separating
            if (velocityAlongNormal > 0) continue

            // Calculate restitution (bounciness)
            const restitution = 0.8
            const impulseScalar = -(1 + restitution) * velocityAlongNormal
            const totalMass = p1.mass + p2.mass
            const impulse = normal.clone().multiplyScalar(impulseScalar / totalMass)

            // Apply impulse
            p1.velocity.add(impulse.clone().multiplyScalar(p2.mass))
            p2.velocity.sub(impulse.clone().multiplyScalar(p1.mass))

            // Add some energy for visual effect
            const energyBoost = isDark ? 0.02 : 0.01
            p1.velocity.add(normal.clone().multiplyScalar(-energyBoost))
            p2.velocity.add(normal.clone().multiplyScalar(energyBoost))
          }
        }
      }
    },
    [isDark],
  )

  // Boundary collision detection
  const checkBoundaryCollisions = useCallback(
    (particle: Particle) => {
      const halfBounds = bounds / 2

      // X boundaries
      if (particle.position.x > halfBounds) {
        particle.position.x = halfBounds
        particle.velocity.x *= -damping
      } else if (particle.position.x < -halfBounds) {
        particle.position.x = -halfBounds
        particle.velocity.x *= -damping
      }

      // Y boundaries
      if (particle.position.y > halfBounds) {
        particle.position.y = halfBounds
        particle.velocity.y *= -damping
      } else if (particle.position.y < -halfBounds) {
        particle.position.y = -halfBounds
        particle.velocity.y *= -damping * 0.5 // Less bouncy on ground
      }

      // Z boundaries
      if (particle.position.z > halfBounds) {
        particle.position.z = halfBounds
        particle.velocity.z *= -damping
      } else if (particle.position.z < -halfBounds) {
        particle.position.z = -halfBounds
        particle.velocity.z *= -damping
      }
    },
    [bounds, damping],
  )

  // Update positions and colors for rendering
  const updateGeometry = useCallback((particles: Particle[]) => {
    if (!ref.current) return

    const positions = ref.current.geometry.attributes.position.array as Float32Array
    const colors = ref.current.geometry.attributes.color.array as Float32Array

    for (let i = 0; i < particles.length; i++) {
      const particle = particles[i]
      const i3 = i * 3

      // Update positions
      positions[i3] = particle.position.x
      positions[i3 + 1] = particle.position.y
      positions[i3 + 2] = particle.position.z

      // Update colors with life-based alpha
      const lifeRatio = particle.life / particle.maxLife
      const alpha = Math.sin(lifeRatio * Math.PI) // Fade in and out

      colors[i3] = particle.color.r * alpha
      colors[i3 + 1] = particle.color.g * alpha
      colors[i3 + 2] = particle.color.b * alpha
    }

    ref.current.geometry.attributes.position.needsUpdate = true
    ref.current.geometry.attributes.color.needsUpdate = true
  }, [])

  // Physics simulation
  useFrame((state, delta) => {
    const time = state.clock.getElapsedTime()

    // Update each particle
    particles.forEach((particle) => {
      // Apply forces
      if (isDark) {
        // Dark theme: Add electromagnetic-like forces
        const electricField = new THREE.Vector3(
          Math.sin(time * 2 + particle.position.x) * 0.0005,
          Math.cos(time * 1.5 + particle.position.y) * 0.0005,
          Math.sin(time * 3 + particle.position.z) * 0.0005,
        )
        particle.acceleration.add(electricField)
      } else {
        // Light theme: Add gentle wind forces
        const windForce = new THREE.Vector3(
          Math.sin(time * 0.5) * 0.0002,
          Math.sin(time * 0.3) * 0.0001,
          Math.cos(time * 0.4) * 0.0002,
        )
        particle.acceleration.add(windForce)
      }

      // Update velocity and position
      particle.velocity.add(particle.acceleration.clone().multiplyScalar(delta * 60))
      particle.velocity.multiplyScalar(damping)
      particle.position.add(particle.velocity.clone().multiplyScalar(delta * 60))

      // Reset acceleration
      particle.acceleration.set(0, gravity, 0)

      // Update life
      particle.life += delta * 60
      if (particle.life > particle.maxLife) {
        // Respawn particle
        particle.position.set((Math.random() - 0.5) * bounds, bounds / 2, (Math.random() - 0.5) * bounds)
        particle.velocity.set((Math.random() - 0.5) * 0.02, -Math.random() * 0.01, (Math.random() - 0.5) * 0.02)
        particle.life = 0
      }

      // Check boundary collisions
      checkBoundaryCollisions(particle)
    })

    // Check particle-to-particle collisions
    checkParticleCollisions(particles)

    // Update geometry
    updateGeometry(particles)
  })

  // Create initial geometry
  const [positions, colors] = useMemo(() => {
    const positions = new Float32Array(count * 3)
    const colors = new Float32Array(count * 3)

    particles.forEach((particle, i) => {
      const i3 = i * 3
      positions[i3] = particle.position.x
      positions[i3 + 1] = particle.position.y
      positions[i3 + 2] = particle.position.z

      colors[i3] = particle.color.r
      colors[i3 + 1] = particle.color.g
      colors[i3 + 2] = particle.color.b
    })

    return [positions, colors]
  }, [particles, count])

  return (
    <Points ref={ref} positions={positions} colors={colors}>
      <PointMaterial
        transparent
        size={isDark ? 0.06 : 0.04}
        sizeAttenuation={true}
        depthWrite={false}
        blending={isDark ? THREE.AdditiveBlending : THREE.NormalBlending}
        vertexColors
        opacity={isDark ? 0.8 : 0.6}
      />
    </Points>
  )
}
