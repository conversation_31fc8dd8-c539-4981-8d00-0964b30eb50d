"use client"

import { useTheme } from "next-themes"
import { useEffect, useState } from "react"

type ColorScheme = {
  primary: string
  secondary: string
  accent1: string
  accent2: string
  accent3: string
  accent4: string
  background: string
}

export function useThemeColors() {
  const { theme, resolvedTheme } = useTheme()
  const [colors, setColors] = useState<ColorScheme>({
    primary: "#f97316", // orange-500
    secondary: "#8b5cf6", // purple-500
    accent1: "#06b6d4", // cyan-500
    accent2: "#f59e0b", // amber-500
    accent3: "#ec4899", // pink-500
    accent4: "#10b981", // emerald-500
    background: "#ffffff", // white
  })

  useEffect(() => {
    const isDark = resolvedTheme === "dark"

    setColors({
      primary: isDark ? "#fb923c" : "#f97316", // orange-400 : orange-500
      secondary: isDark ? "#a78bfa" : "#8b5cf6", // purple-400 : purple-500
      accent1: isDark ? "#22d3ee" : "#06b6d4", // cyan-400 : cyan-500
      accent2: isDark ? "#fbbf24" : "#f59e0b", // amber-400 : amber-500
      accent3: isDark ? "#f472b6" : "#ec4899", // pink-400 : pink-500
      accent4: isDark ? "#34d399" : "#10b981", // emerald-400 : emerald-500
      background: isDark ? "#111827" : "#ffffff", // gray-900 : white
    })
  }, [theme, resolvedTheme])

  return colors
}
