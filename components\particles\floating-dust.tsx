"use client"

import { useRef, useMemo } from "react"
import { useFrame } from "@react-three/fiber"
import { Points, PointMaterial } from "@react-three/drei"
import { useTheme } from "next-themes"
import type * as THREE from "three"

interface FloatingDustProps {
  count?: number
  area?: number
}

export function FloatingDust({ count = 200, area = 10 }: FloatingDustProps) {
  const ref = useRef<THREE.Points>(null)
  const { resolvedTheme } = useTheme()
  const isDark = resolvedTheme === "dark"

  const [positions, velocities] = useMemo(() => {
    const positions = new Float32Array(count * 3)
    const velocities = new Float32Array(count * 3)

    for (let i = 0; i < count; i++) {
      const i3 = i * 3

      // Random positions within area
      positions[i3] = (Math.random() - 0.5) * area
      positions[i3 + 1] = (Math.random() - 0.5) * area
      positions[i3 + 2] = (Math.random() - 0.5) * area

      // Random velocities
      velocities[i3] = (Math.random() - 0.5) * 0.02
      velocities[i3 + 1] = Math.random() * 0.01 + 0.005 // Slight upward drift
      velocities[i3 + 2] = (Math.random() - 0.5) * 0.02
    }

    return [positions, velocities]
  }, [count, area])

  useFrame(() => {
    if (!ref.current) return

    const positions = ref.current.geometry.attributes.position.array as Float32Array

    for (let i = 0; i < count; i++) {
      const i3 = i * 3

      // Update positions based on velocities
      positions[i3] += velocities[i3]
      positions[i3 + 1] += velocities[i3 + 1]
      positions[i3 + 2] += velocities[i3 + 2]

      // Wrap around boundaries
      if (positions[i3] > area / 2) positions[i3] = -area / 2
      if (positions[i3] < -area / 2) positions[i3] = area / 2
      if (positions[i3 + 1] > area / 2) positions[i3 + 1] = -area / 2
      if (positions[i3 + 2] > area / 2) positions[i3 + 2] = -area / 2
      if (positions[i3 + 2] < -area / 2) positions[i3 + 2] = area / 2
    }

    ref.current.geometry.attributes.position.needsUpdate = true
  })

  return (
    <Points ref={ref} positions={positions}>
      <PointMaterial
        transparent
        size={isDark ? 0.03 : 0.02}
        color={isDark ? "#64748b" : "#e2e8f0"}
        opacity={isDark ? 0.3 : 0.2}
        sizeAttenuation={true}
        depthWrite={false}
      />
    </Points>
  )
}
