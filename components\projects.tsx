"use client"

import { useEffect, useRef, useState } from "react"
import dynamic from "next/dynamic"
import { gsap } from "gsap"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ExternalLink, Github } from "lucide-react"
import Image from "next/image"
import { useThemeColors } from "@/hooks/use-theme-colors"
import { useTheme } from "next-themes"

// Dynamically import Three.js components with SSR disabled
const Canvas = dynamic(() => import("@react-three/fiber").then((mod) => ({ default: mod.Canvas })), {
  ssr: false,
})
const Float = dynamic(() => import("@react-three/drei").then((mod) => ({ default: mod.Float })), {
  ssr: false,
})
const Octahedron = dynamic(() => import("@react-three/drei").then((mod) => ({ default: mod.Octahedron })), {
  ssr: false,
})
const Dodecahedron = dynamic(() => import("@react-three/drei").then((mod) => ({ default: mod.Dodecahedron })), {
  ssr: false,
})

function ProjectShapes() {
  const colors = useThemeColors()

  return (
    <>
      <Float speed={1.2} rotationIntensity={0.4} floatIntensity={0.4} position={[-4, 1, 0]}>
        <Octahedron args={[0.4]}>
          <meshStandardMaterial color={colors.primary} />
        </Octahedron>
      </Float>
      <Float speed={0.8} rotationIntensity={0.2} floatIntensity={0.2} position={[4, -1, 0]}>
        <Dodecahedron args={[0.3]}>
          <meshStandardMaterial color={colors.secondary} />
        </Dodecahedron>
      </Float>
    </>
  )
}

export default function Projects() {
  const projectsRef = useRef<HTMLDivElement>(null)
  const [mounted, setMounted] = useState(false)
  const { resolvedTheme } = useTheme()

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted || typeof window === "undefined") return

    const ctx = gsap.context(() => {
      gsap.fromTo(
        ".project-card",
        { opacity: 0, y: 50, rotationY: 15 },
        {
          opacity: 1,
          y: 0,
          rotationY: 0,
          duration: 1,
          ease: "power2.out",
          stagger: 0.3,
          scrollTrigger: {
            trigger: ".projects-grid",
            start: "top 80%",
          },
        },
      )
    }, projectsRef)

    return () => ctx.revert()
  }, [])

  const projects = [
    {
      title: "E-commerce Platform",
      description: "A modern e-commerce platform built with Next.js and Stripe integration.",
      image: "/placeholder.svg?height=300&width=400",
      tags: ["Next.js", "TypeScript", "Stripe", "Tailwind CSS"],
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      title: "Task Management App",
      description: "A collaborative task management application with real-time updates.",
      image: "/placeholder.svg?height=300&width=400",
      tags: ["React", "Node.js", "Socket.io", "MongoDB"],
      liveUrl: "#",
      githubUrl: "#",
    },
    {
      title: "Portfolio Website",
      description: "A creative portfolio website with 3D animations and smooth interactions.",
      image: "/placeholder.svg?height=300&width=400",
      tags: ["Three.js", "GSAP", "React", "WebGL"],
      liveUrl: "#",
      githubUrl: "#",
    },
  ]

  return (
    <section id="projects" ref={projectsRef} className="py-20 bg-white dark:bg-gray-900 relative overflow-hidden">
      {/* 3D Background */}
      {mounted && (
        <div className="absolute inset-0 opacity-20">
          <Canvas camera={{ position: [0, 0, 5] }}>
            <ambientLight intensity={resolvedTheme === "dark" ? 0.4 : 0.5} />
            <pointLight position={[10, 10, 10]} intensity={resolvedTheme === "dark" ? 0.8 : 1.0} />
            <ProjectShapes />
          </Canvas>
        </div>
      )}

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-4 mb-16 animate-on-scroll">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white">
            Recent
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-purple-600 dark:from-orange-400 dark:to-purple-500 ml-4">
              Projects
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Here are some of my recent projects that showcase my skills and creativity.
          </p>
        </div>

        <div className="projects-grid grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <Card
              key={index}
              className="project-card group hover:shadow-2xl transition-all duration-500 border-0 bg-white dark:bg-gray-800 overflow-hidden"
            >
              <div className="relative overflow-hidden">
                <Image
                  src={project.image || "/placeholder.svg"}
                  alt={project.title}
                  width={400}
                  height={300}
                  className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4 flex space-x-2">
                    <Button
                      size="sm"
                      className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/30"
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Live Demo
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/30"
                    >
                      <Github className="w-4 h-4 mr-2" />
                      Code
                    </Button>
                  </div>
                </div>
              </div>
              <CardContent className="p-6 space-y-4">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-orange-500 dark:group-hover:text-orange-400 transition-colors duration-300">
                  {project.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">{project.description}</p>
                <div className="flex flex-wrap gap-2">
                  {project.tags.map((tag, tagIndex) => (
                    <span
                      key={tagIndex}
                      className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full font-medium"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12 animate-on-scroll">
          <Button
            size="lg"
            variant="outline"
            className="border-orange-500 dark:border-orange-400 text-orange-500 dark:text-orange-400 hover:bg-orange-500 dark:hover:bg-orange-600 hover:text-white px-8 py-3"
          >
            View All Projects
          </Button>
        </div>
      </div>
    </section>
  )
}
