"use client"

import { useState, useEffect } from "react"
import { Canvas } from "@react-three/fiber"
import { Float, Sphere, Box, Torus, Cone } from "@react-three/drei"
import { useThemeColors } from "@/hooks/use-theme-colors"
import { useTheme } from "next-themes"
import { ThemeParticles } from "@/components/particles/theme-particles"
import { FloatingDust } from "@/components/particles/floating-dust"
import { EnergyField } from "@/components/particles/energy-field"
import { PhysicsParticles } from "@/components/particles/physics-particles"
import { InteractiveField } from "@/components/particles/interactive-field"
import { CollisionSpheres } from "@/components/particles/collision-spheres"
import { DepthPhysics } from "@/components/particles/depth-physics"
import { LayeredField } from "@/components/particles/layered-field"
import { ThemeAwareParticles } from "@/components/particles/theme-aware-particles"

function BackgroundShapes() {
  const colors = useThemeColors()

  return (
    <>
      {/* Floating geometric shapes with depth positioning */}
      <Float speed={0.5} rotationIntensity={0.1} floatIntensity={0.1} position={[-8, 4, -5]}>
        <Sphere args={[0.2, 16, 16]}>
          <meshStandardMaterial color={colors.primary} transparent opacity={0.6} />
        </Sphere>
      </Float>

      <Float speed={0.8} rotationIntensity={0.2} floatIntensity={0.2} position={[8, -3, -8]}>
        <Box args={[0.3, 0.3, 0.3]}>
          <meshStandardMaterial color={colors.secondary} transparent opacity={0.5} />
        </Box>
      </Float>

      <Float speed={0.6} rotationIntensity={0.15} floatIntensity={0.15} position={[-6, -4, -12]}>
        <Torus args={[0.2, 0.08, 16, 32]}>
          <meshStandardMaterial color={colors.accent1} transparent opacity={0.4} />
        </Torus>
      </Float>

      <Float speed={0.7} rotationIntensity={0.18} floatIntensity={0.18} position={[6, 5, -10]}>
        <Cone args={[0.2, 0.4, 8]}>
          <meshStandardMaterial color={colors.accent2} transparent opacity={0.5} />
        </Cone>
      </Float>

      <Float speed={0.4} rotationIntensity={0.12} floatIntensity={0.12} position={[0, 6, -15]}>
        <Sphere args={[0.15, 16, 16]}>
          <meshStandardMaterial color={colors.accent3} transparent opacity={0.3} />
        </Sphere>
      </Float>

      <Float speed={0.9} rotationIntensity={0.25} floatIntensity={0.25} position={[-4, -6, -7]}>
        <Box args={[0.25, 0.25, 0.25]}>
          <meshStandardMaterial color={colors.accent4} transparent opacity={0.6} />
        </Box>
      </Float>
    </>
  )
}

export default function FloatingElements() {
  const [mounted, setMounted] = useState(false)
  const { resolvedTheme } = useTheme()

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return null
  }

  return (
    <div className="fixed inset-0 pointer-events-none z-0">
      <Canvas camera={{ position: [0, 0, 10], fov: 75 }}>
        <ambientLight intensity={resolvedTheme === "dark" ? 0.2 : 0.3} />
        <pointLight position={[10, 10, 10]} intensity={resolvedTheme === "dark" ? 0.4 : 0.5} />
        <pointLight position={[-10, -10, -10]} intensity={resolvedTheme === "dark" ? 0.2 : 0.3} />

        {/* Depth-based lighting for better depth perception */}
        <pointLight position={[0, 0, -5]} intensity={0.2} color={resolvedTheme === "dark" ? "#4f46e5" : "#f97316"} />
        <pointLight position={[0, 0, -15]} intensity={0.1} color={resolvedTheme === "dark" ? "#ec4899" : "#f59e0b"} />

        {/* Theme-aware particles with smooth transitions */}
        <ThemeAwareParticles count={150} themeChangeSpeed={1.5} />

        {/* Depth-enabled particle systems */}
        <DepthPhysics count={200} depthLayers={5} maxDepth={20} parallaxStrength={0.3} />
        <LayeredField layers={3} particlesPerLayer={60} layerSpacing={4} />

        {/* Original physics-enabled particle systems */}
        <PhysicsParticles count={100} bounds={12} gravity={-0.0005} damping={0.99} />
        <InteractiveField count={80} mouseInfluence={3} attractionForce={0.002} />
        <CollisionSpheres count={2} bounds={10} />

        {/* Original particle systems with reduced counts for performance */}
        <ThemeParticles count={150} speed={0.3} size={0.015} />
        <FloatingDust count={60} area={15} />
        <EnergyField count={80} radius={8} />

        <BackgroundShapes />
      </Canvas>
    </div>
  )
}
