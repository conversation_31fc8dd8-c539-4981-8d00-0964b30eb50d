"use client"

import { useRef, useMemo } from "react"
import { use<PERSON><PERSON><PERSON>, useThree } from "@react-three/fiber"
import { Points, PointMaterial } from "@react-three/drei"
import { useTheme } from "next-themes"
import * as THREE from "three"

interface LayeredFieldProps {
  layers?: number
  particlesPerLayer?: number
  layerSpacing?: number
}

export function LayeredField({ layers = 4, particlesPerLayer = 100, layerSpacing = 3 }: LayeredFieldProps) {
  const groupRef = useRef<THREE.Group>(null)
  const { resolvedTheme } = useTheme()
  const { camera, mouse, viewport } = useThree()
  const isDark = resolvedTheme === "dark"

  // Create depth layers with different properties
  const layerSystems = useMemo(() => {
    return Array.from({ length: layers }, (_, layerIndex) => {
      const depth = -(layerIndex + 1) * layerSpacing
      const particles = []

      for (let i = 0; i < particlesPerLayer; i++) {
        const angle = (i / particlesPer<PERSON>ayer) * Math.PI * 2
        const radius = 2 + layerIndex * 1.5
        const variation = (Math.random() - 0.5) * radius * 0.5

        particles.push({
          position: new THREE.Vector3(
            Math.cos(angle) * (radius + variation),
            Math.sin(angle) * (radius + variation) * 0.5,
            depth + (Math.random() - 0.5) * 0.5,
          ),
          originalPosition: new THREE.Vector3(
            Math.cos(angle) * (radius + variation),
            Math.sin(angle) * (radius + variation) * 0.5,
            depth,
          ),
          velocity: new THREE.Vector3(0, 0, 0),
          angle: angle,
          radius: radius + variation,
          layer: layerIndex,
          depth: Math.abs(depth),
          phase: Math.random() * Math.PI * 2,
          size: 0.02 + layerIndex * 0.01,
          opacity: 1 - layerIndex * 0.15,
        })
      }

      return {
        particles,
        layerIndex,
        depth,
        rotationSpeed: 0.1 + layerIndex * 0.05,
        oscillationSpeed: 0.5 + layerIndex * 0.2,
        parallaxStrength: 1 - layerIndex * 0.2,
      }
    })
  }, [layers, particlesPerLayer, layerSpacing])

  // Depth-based interaction between layers
  const calculateLayerInteractions = (systems: any[], time: number) => {
    systems.forEach((system, systemIndex) => {
      system.particles.forEach((particle: any) => {
        // Calculate mouse influence based on depth
        const mouseWorldX = (mouse.x * viewport.width) / 2
        const mouseWorldY = (mouse.y * viewport.height) / 2

        // Parallax effect - closer layers move more
        const parallaxX = mouseWorldX * system.parallaxStrength * 0.1
        const parallaxY = mouseWorldY * system.parallaxStrength * 0.1

        // Apply parallax offset
        const targetX = particle.originalPosition.x + parallaxX
        const targetY = particle.originalPosition.y + parallaxY

        // Spring force to parallax position
        const springForce = new THREE.Vector3(
          (targetX - particle.position.x) * 0.002,
          (targetY - particle.position.y) * 0.002,
          0,
        )
        particle.velocity.add(springForce)

        // Layer-specific rotation
        const rotationForce = new THREE.Vector3(
          -particle.position.y * system.rotationSpeed * 0.0001,
          particle.position.x * system.rotationSpeed * 0.0001,
          0,
        )
        particle.velocity.add(rotationForce)

        // Depth-based oscillation
        const oscillation = Math.sin(time * system.oscillationSpeed + particle.phase) * 0.0002
        particle.velocity.add(
          new THREE.Vector3(
            Math.cos(particle.angle + time * 0.1) * oscillation,
            Math.sin(particle.angle + time * 0.1) * oscillation,
            Math.sin(time * 2 + particle.phase) * 0.0001,
          ),
        )

        // Inter-layer attraction/repulsion
        systems.forEach((otherSystem, otherIndex) => {
          if (systemIndex === otherIndex) return

          const layerDistance = Math.abs(systemIndex - otherIndex)
          const influence = 1 / (layerDistance + 1)

          otherSystem.particles.forEach((otherParticle: any) => {
            const distance = particle.position.distanceTo(otherParticle.position)
            if (distance < 2 && distance > 0.1) {
              const force = new THREE.Vector3().subVectors(otherParticle.position, particle.position).normalize()

              // Attraction between adjacent layers, repulsion between distant layers
              const forceStrength = layerDistance === 1 ? 0.00001 : -0.00005
              force.multiplyScalar((forceStrength * influence) / (distance * distance))

              particle.velocity.add(force)
            }
          })
        })

        // Apply damping
        particle.velocity.multiplyScalar(0.98)

        // Update position
        particle.position.add(particle.velocity)

        // Depth boundaries
        const maxZ = system.depth + 1
        const minZ = system.depth - 1
        if (particle.position.z > maxZ) {
          particle.position.z = maxZ
          particle.velocity.z *= -0.5
        }
        if (particle.position.z < minZ) {
          particle.position.z = minZ
          particle.velocity.z *= -0.5
        }
      })
    })
  }

  useFrame((state, delta) => {
    if (!groupRef.current) return

    const time = state.clock.getElapsedTime()

    // Calculate layer interactions
    calculateLayerInteractions(layerSystems, time)

    // Update each layer's visual representation
    layerSystems.forEach((system, systemIndex) => {
      const pointsRef = groupRef.current?.children[systemIndex] as THREE.Points
      if (!pointsRef) return

      const positions = pointsRef.geometry.attributes.position.array as Float32Array
      const colors = pointsRef.geometry.attributes.color.array as Float32Array

      system.particles.forEach((particle: any, i: number) => {
        const i3 = i * 3

        // Update positions
        positions[i3] = particle.position.x
        positions[i3 + 1] = particle.position.y
        positions[i3 + 2] = particle.position.z

        // Calculate depth-based color
        const distanceToCamera = particle.position.distanceTo(camera.position)
        const depthFactor = 1 - Math.min(distanceToCamera / (layerSpacing * layers), 1)
        const velocityFactor = Math.min(particle.velocity.length() * 100, 1)

        // Layer-specific colors
        const hue = (systemIndex / layers) * 0.6 + time * 0.1
        const saturation = 0.7 * depthFactor
        const lightness = (isDark ? 0.6 : 0.4) * (0.5 + depthFactor * 0.5) * particle.opacity

        const color = new THREE.Color().setHSL(hue, saturation, lightness + velocityFactor * 0.2)
        colors[i3] = color.r
        colors[i3 + 1] = color.g
        colors[i3 + 2] = color.b
      })

      pointsRef.geometry.attributes.position.needsUpdate = true
      pointsRef.geometry.attributes.color.needsUpdate = true
    })
  })

  return (
    <group ref={groupRef}>
      {layerSystems.map((system, index) => {
        const positions = new Float32Array(particlesPerLayer * 3)
        const colors = new Float32Array(particlesPerLayer * 3)

        system.particles.forEach((particle, i) => {
          const i3 = i * 3
          positions[i3] = particle.position.x
          positions[i3 + 1] = particle.position.y
          positions[i3 + 2] = particle.position.z

          const hue = (index / layers) * 0.6
          const color = new THREE.Color().setHSL(hue, 0.7, isDark ? 0.6 : 0.4)
          colors[i3] = color.r
          colors[i3 + 1] = color.g
          colors[i3 + 2] = color.b
        })

        return (
          <Points key={index} positions={positions} colors={colors}>
            <PointMaterial
              transparent
              size={0.03 + index * 0.01}
              sizeAttenuation={true}
              depthWrite={false}
              blending={isDark ? THREE.AdditiveBlending : THREE.NormalBlending}
              vertexColors
              opacity={1 - index * 0.15}
            />
          </Points>
        )
      })}
    </group>
  )
}
