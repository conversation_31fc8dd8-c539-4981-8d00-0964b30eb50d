"use client"

import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import { useTheme } from "next-themes"

interface ProfileCardProps {
  name?: string
  title?: string
  username?: string
  avatar?: string
  profileImage?: string
  isOnline?: boolean
}

export function ProfileCard({
  name = "<PERSON>",
  title = "Software Engineer",
  username = "@alexcodes",
  avatar = "/placeholder.svg?height=40&width=40",
  profileImage = "/placeholder.svg?height=300&width=250",
  isOnline = true,
}: ProfileCardProps) {
  const [particles, setParticles] = useState<Array<{ id: number; x: number; y: number; delay: number }>>([])
  const [mounted, setMounted] = useState(false)
  const { resolvedTheme } = useTheme()
  const isDark = resolvedTheme === "dark"

  useEffect(() => {
    setMounted(true)
    // Generate random particles for animation
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 2,
    }))
    setParticles(newParticles)
  }, [])

  if (!mounted) {
    return (
      <div className="w-full max-w-sm mx-auto h-96 bg-slate-800 rounded-3xl animate-pulse">
        <div className="p-6 space-y-4">
          <div className="h-6 bg-slate-700 rounded w-3/4 mx-auto"></div>
          <div className="h-4 bg-slate-700 rounded w-1/2 mx-auto"></div>
          <div className="h-64 bg-slate-700 rounded-xl"></div>
          <div className="h-16 bg-slate-700 rounded-2xl"></div>
        </div>
      </div>
    )
  }

  // Theme-aware colors
  const glowColor = isDark ? "rgba(34, 211, 238, 0.25)" : "rgba(34, 211, 238, 0.3)"
  const borderColor = isDark ? "border-cyan-500/40" : "border-cyan-400/50"
  const particleColor = isDark ? "bg-cyan-500" : "bg-cyan-400"
  const buttonGradient = isDark
    ? "from-cyan-600 to-blue-700 hover:from-cyan-700 hover:to-blue-800"
    : "from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700"
  const buttonShadow = isDark ? "rgba(34, 211, 238, 0.3)" : "rgba(34, 211, 238, 0.4)"
  const bgGradient = isDark ? "from-slate-900 to-slate-950" : "from-slate-800 to-slate-900"

  return (
    <div className="relative w-full max-w-sm mx-auto">
      {/* Glowing background effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 via-blue-500/20 to-purple-500/20 rounded-3xl blur-xl"></div>

      {/* Main card */}
      <div
        className={`relative bg-gradient-to-b ${bgGradient} rounded-3xl p-6 border ${borderColor} shadow-2xl overflow-hidden`}
        style={{
          boxShadow: `0 0 50px ${glowColor}, inset 0 1px 0 rgba(255, 255, 255, 0.1)`,
        }}
      >
        {/* Animated particles */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {particles.map((particle) => (
            <div
              key={particle.id}
              className={`absolute w-1 h-1 ${particleColor} rounded-full opacity-60 animate-pulse`}
              style={{
                left: `${particle.x}%`,
                top: `${particle.y}%`,
                animationDelay: `${particle.delay}s`,
                animationDuration: "3s",
              }}
            />
          ))}
        </div>

        {/* Header */}
        <div className="text-center mb-6 relative z-10">
          <h2 className="text-2xl font-bold text-white mb-2">{name}</h2>
          <p className="text-gray-300 text-sm">{title}</p>
        </div>

        {/* Profile Image */}
        <div className="relative mb-6 flex justify-center">
          <div className="relative">
            {/* Glowing border around image */}
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-2xl blur-sm opacity-75"></div>
            <div className="relative bg-slate-800 rounded-2xl p-1">
              <Image
                src={profileImage || "/placeholder.svg"}
                alt={name}
                width={250}
                height={300}
                className="w-full h-64 object-cover rounded-xl"
              />
            </div>

            {/* Floating particles around image */}
            <div className="absolute top-4 right-4 pointer-events-none">
              <div className="flex space-x-1">
                {[...Array(3)].map((_, i) => (
                  <div
                    key={i}
                    className={`w-2 h-2 ${particleColor} rounded-full animate-bounce`}
                    style={{ animationDelay: `${i * 0.2}s` }}
                  />
                ))}
              </div>
            </div>

            <div className="absolute bottom-4 left-4 pointer-events-none">
              <div className="flex space-x-1">
                {[...Array(4)].map((_, i) => (
                  <div
                    key={i}
                    className={`w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse`}
                    style={{ animationDelay: `${i * 0.3}s` }}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="bg-slate-700/50 rounded-2xl p-4 backdrop-blur-sm border border-slate-600/50 relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Image
                  src={avatar || "/placeholder.svg"}
                  alt={name}
                  width={40}
                  height={40}
                  className={`w-10 h-10 rounded-full border-2 ${borderColor}`}
                />
                {isOnline && (
                  <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-slate-800 animate-pulse"></div>
                )}
              </div>
              <div>
                <p className="text-white text-sm font-medium">{username}</p>
                <p className="text-green-400 text-xs">{isOnline ? "Online" : "Offline"}</p>
              </div>
            </div>

            <Button
              className={`bg-gradient-to-r ${buttonGradient} text-white border-0 px-6 py-2 text-sm font-medium transition-all duration-300 hover:scale-105`}
              style={{
                boxShadow: `0 4px 15px ${buttonShadow}`,
              }}
            >
              Contact Me
            </Button>
          </div>
        </div>

        {/* Subtle inner glow */}
        <div className="absolute inset-0 rounded-3xl bg-gradient-to-t from-transparent via-transparent to-cyan-500/5 pointer-events-none"></div>
      </div>
    </div>
  )
}
