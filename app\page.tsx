"use client"

import { useEffect, useRef, useState } from "react"
import { gsap } from "gsap"
import Hero from "@/components/hero"
import About from "@/components/about"
import Services from "@/components/services"
import Projects from "@/components/projects"
import Contact from "@/components/contact"
import Navigation from "@/components/navigation"
import FloatingElements from "@/components/floating-elements"

export default function Portfolio() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted || typeof window === "undefined") return

    // Dynamically import and register ScrollTrigger only on client
    const initGSAP = async () => {
      try {
        const { ScrollTrigger } = await import("gsap/ScrollTrigger")
        gsap.registerPlugin(ScrollTrigger)

        const ctx = gsap.context(() => {
          // Smooth scroll animations
          const elements = document.querySelectorAll(".animate-on-scroll")
          elements.forEach((element) => {
            gsap.fromTo(
              element,
              { opacity: 0, y: 50 },
              {
                opacity: 1,
                y: 0,
                duration: 1,
                ease: "power2.out",
                scrollTrigger: {
                  trigger: element,
                  start: "top 80%",
                  end: "bottom 20%",
                  toggleActions: "play none none reverse",
                },
              },
            )
          })

          // Parallax effect for floating elements
          const parallaxElements = document.querySelectorAll(".parallax")
          parallaxElements.forEach((element) => {
            gsap.to(element, {
              yPercent: -50,
              ease: "none",
              scrollTrigger: {
                trigger: element,
                start: "top bottom",
                end: "bottom top",
                scrub: true,
              },
            })
          })
        }, containerRef)

        return () => ctx.revert()
      } catch (error) {
        console.warn("GSAP ScrollTrigger failed to load:", error)
      }
    }

    const cleanup = initGSAP()
    return () => {
      cleanup?.then((fn) => fn?.())
    }
  }, [mounted])

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div ref={containerRef} className="relative">
      <Navigation />
      <FloatingElements />

      <main className="relative z-10">
        <Hero />
        <About />
        <Services />
        <Projects />
        <Contact />
      </main>
    </div>
  )
}
