"use client"

import { useEffect, useRef, useState } from "react"
import dynamic from "next/dynamic"
import { gsap } from "gsap"
import { useThemeColors } from "@/hooks/use-theme-colors"
import { useTheme } from "next-themes"

// Dynamically import Three.js components with SSR disabled
const Canvas = dynamic(() => import("@react-three/fiber").then((mod) => ({ default: mod.Canvas })), {
  ssr: false,
})
const Float = dynamic(() => import("@react-three/drei").then((mod) => ({ default: mod.Float })), {
  ssr: false,
})
const Sphere = dynamic(() => import("@react-three/drei").then((mod) => ({ default: mod.Sphere })), {
  ssr: false,
})
const Box = dynamic(() => import("@react-three/drei").then((mod) => ({ default: mod.Box })), {
  ssr: false,
})

function FloatingShapes() {
  const colors = useThemeColors()

  return (
    <>
      <Float speed={1.5} rotationIntensity={0.3} floatIntensity={0.3} position={[-2, 1, 0]}>
        <Sphere args={[0.3, 32, 32]}>
          <meshStandardMaterial color={colors.primary} />
        </Sphere>
      </Float>
      <Float speed={2} rotationIntensity={0.5} floatIntensity={0.5} position={[2, -1, 0]}>
        <Box args={[0.4, 0.4, 0.4]}>
          <meshStandardMaterial color={colors.secondary} />
        </Box>
      </Float>
    </>
  )
}

export default function About() {
  const aboutRef = useRef<HTMLDivElement>(null)
  const [mounted, setMounted] = useState(false)
  const { resolvedTheme } = useTheme()

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted || typeof window === "undefined") return

    const ctx = gsap.context(() => {
      gsap.fromTo(
        ".about-content",
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power2.out",
          scrollTrigger: {
            trigger: ".about-content",
            start: "top 80%",
          },
        },
      )
    }, aboutRef)

    return () => ctx.revert()
  }, [])

  return (
    <section id="about" ref={aboutRef} className="py-20 bg-white dark:bg-gray-900 relative overflow-hidden">
      {/* 3D Background */}
      {mounted && (
        <div className="absolute inset-0 opacity-20">
          <Canvas camera={{ position: [0, 0, 5] }}>
            <ambientLight intensity={resolvedTheme === "dark" ? 0.4 : 0.5} />
            <pointLight position={[10, 10, 10]} intensity={resolvedTheme === "dark" ? 0.8 : 1.0} />
            <FloatingShapes />
          </Canvas>
        </div>
      )}

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="about-content text-center space-y-12">
          <div className="space-y-4">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white">
              Clients Get Always
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-purple-600 dark:from-orange-400 dark:to-purple-500">
                Exceptional Works From Me
              </span>
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Lorem ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the
              industry's standard dummy text ever since the 1500s.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center space-y-2">
              <div className="text-3xl font-bold text-gray-900 dark:text-white">1.</div>
              <div className="text-lg font-semibold text-gray-800 dark:text-gray-200">Top Quality Works</div>
            </div>
            <div className="text-center space-y-2">
              <div className="text-3xl font-bold text-gray-900 dark:text-white">2.</div>
              <div className="text-lg font-semibold text-gray-800 dark:text-gray-200">Commitments</div>
            </div>
            <div className="text-center space-y-2">
              <div className="text-3xl font-bold text-gray-900 dark:text-white">3.</div>
              <div className="text-lg font-semibold text-gray-800 dark:text-gray-200">24 Hours active</div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-orange-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-2xl p-8">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="space-y-4">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Perfect Solution For Your Business</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper
                  mattis.
                </p>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-orange-500 dark:text-orange-400">566.12k</div>
                <div className="text-gray-600 dark:text-gray-300">Number of projects</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
