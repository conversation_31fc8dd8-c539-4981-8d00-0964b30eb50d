"use client"

import { useRef, useMemo } from "react"
import { use<PERSON>rame, useThree } from "@react-three/fiber"
import { Points, PointMaterial } from "@react-three/drei"
import { useTheme } from "next-themes"
import * as THREE from "three"

interface DepthPhysicsProps {
  count?: number
  depthLayers?: number
  maxDepth?: number
  parallaxStrength?: number
}

export function DepthPhysics({
  count = 400,
  depthLayers = 5,
  maxDepth = 15,
  parallaxStrength = 0.5,
}: DepthPhysicsProps) {
  const ref = useRef<THREE.Points>(null)
  const { resolvedTheme } = useTheme()
  const { camera, mouse, viewport } = useThree()
  const isDark = resolvedTheme === "dark"

  // Create layered particle system with depth-based properties
  const particles = useMemo(() => {
    const temp = []

    for (let i = 0; i < count; i++) {
      // Assign particles to depth layers
      const layer = Math.floor(i / (count / depthLayers))
      const depthZ = -((layer + 1) / depthLayers) * maxDepth

      // Position varies by depth layer
      const layerRadius = 3 + layer * 2 // Larger radius for deeper layers
      const angle = Math.random() * Math.PI * 2
      const radius = Math.random() * layerRadius

      const particle = {
        // Position
        position: new THREE.Vector3(Math.cos(angle) * radius, (Math.random() - 0.5) * layerRadius, depthZ),
        originalPosition: new THREE.Vector3(Math.cos(angle) * radius, (Math.random() - 0.5) * layerRadius, depthZ),

        // Physics
        velocity: new THREE.Vector3(0, 0, 0),
        acceleration: new THREE.Vector3(0, 0, 0),

        // Depth properties
        layer: layer,
        depth: Math.abs(depthZ),
        depthRatio: layer / (depthLayers - 1), // 0 to 1

        // Visual properties
        size: 0.02 + layer * 0.01, // Larger particles in back
        mass: 1 + layer * 0.5,

        // Interaction properties
        parallaxSensitivity: 1 - layer * 0.15, // Front layers more sensitive
        mouseSensitivity: 1 - layer * 0.2,

        // Animation properties
        phase: Math.random() * Math.PI * 2,
        frequency: 0.5 + Math.random() * 1.5,

        // Collision properties
        collisionRadius: 0.05 + layer * 0.02,
        lastCollision: 0,

        // Color properties
        baseHue: Math.random(),
        saturation: 0.7 - layer * 0.1,
        lightness: isDark ? 0.6 + layer * 0.1 : 0.5 - layer * 0.1,
      }

      temp.push(particle)
    }

    return temp
  }, [count, depthLayers, maxDepth, isDark])

  // Depth-based collision detection
  const checkDepthCollisions = (particles: any[], delta: number) => {
    for (let i = 0; i < particles.length; i++) {
      const particle = particles[i]

      // Update collision timer
      if (particle.lastCollision > 0) {
        particle.lastCollision -= delta
      }

      for (let j = i + 1; j < particles.length; j++) {
        const other = particles[j]

        // Only check collisions within same or adjacent layers
        const layerDiff = Math.abs(particle.layer - other.layer)
        if (layerDiff > 1) continue

        const distance = particle.position.distanceTo(other.position)
        const minDistance = particle.collisionRadius + other.collisionRadius

        if (distance < minDistance && particle.lastCollision <= 0 && other.lastCollision <= 0) {
          // Calculate collision normal
          const normal = new THREE.Vector3().subVectors(other.position, particle.position).normalize()

          // Depth-based collision response
          const depthFactor = 1 - Math.abs(particle.depth - other.depth) / maxDepth
          const collisionStrength = 0.01 * depthFactor

          // Apply collision forces
          particle.velocity.sub(normal.clone().multiplyScalar(collisionStrength))
          other.velocity.add(normal.clone().multiplyScalar(collisionStrength))

          // Add depth-based separation
          const separation = normal.clone().multiplyScalar(minDistance - distance)
          particle.position.sub(separation.clone().multiplyScalar(0.5))
          other.position.add(separation.clone().multiplyScalar(0.5))

          // Set collision timers
          particle.lastCollision = 0.5
          other.lastCollision = 0.5

          // Depth interaction: particles can "push" between layers
          if (layerDiff === 1) {
            const depthPush = 0.1
            if (particle.layer < other.layer) {
              particle.position.z += depthPush
              other.position.z -= depthPush
            } else {
              particle.position.z -= depthPush
              other.position.z += depthPush
            }
          }
        }
      }
    }
  }

  // Calculate depth-based parallax effect
  const applyParallaxEffect = (particle: any, mouseX: number, mouseY: number) => {
    // Convert mouse position to world coordinates
    const mouseWorldX = (mouseX * viewport.width) / 2
    const mouseWorldY = (mouseY * viewport.height) / 2

    // Calculate parallax offset based on depth
    const parallaxX = mouseWorldX * particle.parallaxSensitivity * parallaxStrength * (particle.depth / maxDepth)
    const parallaxY = mouseWorldY * particle.parallaxSensitivity * parallaxStrength * (particle.depth / maxDepth)

    // Apply parallax as a force toward the offset position
    const targetX = particle.originalPosition.x + parallaxX
    const targetY = particle.originalPosition.y + parallaxY

    const parallaxForce = new THREE.Vector3(
      (targetX - particle.position.x) * 0.001,
      (targetY - particle.position.y) * 0.001,
      0,
    )

    particle.acceleration.add(parallaxForce)
  }

  // Apply depth-based forces
  const applyDepthForces = (particle: any, time: number) => {
    // Depth-based oscillation
    const depthOscillation =
      Math.sin(time * particle.frequency + particle.phase) * 0.0001 * (1 + particle.depth / maxDepth)
    particle.acceleration.add(
      new THREE.Vector3(Math.cos(particle.phase) * depthOscillation, Math.sin(particle.phase) * depthOscillation, 0),
    )

    // Depth-based circular motion
    const circularForce = 0.00005 * (1 - particle.depthRatio)
    particle.acceleration.add(
      new THREE.Vector3(-particle.position.y * circularForce, particle.position.x * circularForce, 0),
    )

    // Depth-based attraction to original position
    const springForce = new THREE.Vector3()
      .subVectors(particle.originalPosition, particle.position)
      .multiplyScalar(0.0005 * (1 + particle.depthRatio))

    particle.acceleration.add(springForce)

    // Depth-based Z-oscillation
    const zOscillation = Math.sin(time * 0.5 + particle.phase) * 0.5 * particle.depthRatio
    particle.position.z = particle.originalPosition.z + zOscillation
  }

  useFrame((state, delta) => {
    if (!ref.current) return

    const time = state.clock.getElapsedTime()
    const positions = ref.current.geometry.attributes.position.array as Float32Array
    const colors = ref.current.geometry.attributes.color.array as Float32Array
    const sizes = ref.current.geometry.attributes.size?.array as Float32Array

    // Check depth-based collisions
    checkDepthCollisions(particles, delta)

    // Update each particle
    particles.forEach((particle, i) => {
      const i3 = i * 3

      // Apply parallax effect
      applyParallaxEffect(particle, mouse.x, mouse.y)

      // Apply depth-based forces
      applyDepthForces(particle, time)

      // Update physics
      particle.velocity.add(particle.acceleration)
      particle.velocity.multiplyScalar(0.98) // Damping
      particle.position.add(particle.velocity)

      // Reset acceleration
      particle.acceleration.set(0, 0, 0)

      // Update position array
      positions[i3] = particle.position.x
      positions[i3 + 1] = particle.position.y
      positions[i3 + 2] = particle.position.z

      // Calculate depth-based color
      const distanceToCamera = particle.position.distanceTo(camera.position)
      const depthFactor = 1 - Math.min(distanceToCamera / maxDepth, 1)
      const velocityFactor = Math.min(particle.velocity.length() * 50, 1)

      // Color based on depth and collision state
      if (particle.lastCollision > 0) {
        // Collision colors
        if (isDark) {
          colors[i3] = 1.0 * depthFactor
          colors[i3 + 1] = 0.5 * depthFactor
          colors[i3 + 2] = 0.2 * depthFactor
        } else {
          colors[i3] = 1.0 * depthFactor
          colors[i3 + 1] = 0.3 * depthFactor
          colors[i3 + 2] = 0.8 * depthFactor
        }
      } else {
        // Normal depth-based colors
        const hue = particle.baseHue + particle.depthRatio * 0.3
        const saturation = particle.saturation * depthFactor
        const lightness = particle.lightness * (0.5 + depthFactor * 0.5) * (0.8 + velocityFactor * 0.2)

        const color = new THREE.Color().setHSL(hue, saturation, lightness)
        colors[i3] = color.r
        colors[i3 + 1] = color.g
        colors[i3 + 2] = color.b
      }

      // Update size based on depth (if size attribute exists)
      if (sizes) {
        const depthSize = particle.size * (0.5 + depthFactor * 0.5)
        const collisionSize = particle.lastCollision > 0 ? 1.5 : 1.0
        sizes[i] = depthSize * collisionSize
      }
    })

    // Update geometry
    ref.current.geometry.attributes.position.needsUpdate = true
    ref.current.geometry.attributes.color.needsUpdate = true
    if (ref.current.geometry.attributes.size) {
      ref.current.geometry.attributes.size.needsUpdate = true
    }
  })

  // Create initial geometry with size attribute
  const [positions, colors, sizes] = useMemo(() => {
    const positions = new Float32Array(count * 3)
    const colors = new Float32Array(count * 3)
    const sizes = new Float32Array(count)

    particles.forEach((particle, i) => {
      const i3 = i * 3

      // Positions
      positions[i3] = particle.position.x
      positions[i3 + 1] = particle.position.y
      positions[i3 + 2] = particle.position.z

      // Colors
      const color = new THREE.Color().setHSL(particle.baseHue, particle.saturation, particle.lightness)
      colors[i3] = color.r
      colors[i3 + 1] = color.g
      colors[i3 + 2] = color.b

      // Sizes
      sizes[i] = particle.size
    })

    return [positions, colors, sizes]
  }, [particles, count])

  return (
    <Points ref={ref} positions={positions} colors={colors}>
      <bufferGeometry>
        <bufferAttribute attach="attributes-position" count={count} array={positions} itemSize={3} />
        <bufferAttribute attach="attributes-color" count={count} array={colors} itemSize={3} />
        <bufferAttribute attach="attributes-size" count={count} array={sizes} itemSize={1} />
      </bufferGeometry>
      <PointMaterial
        transparent
        size={isDark ? 0.06 : 0.04}
        sizeAttenuation={true}
        depthWrite={false}
        blending={isDark ? THREE.AdditiveBlending : THREE.NormalBlending}
        vertexColors
        opacity={0.8}
      />
    </Points>
  )
}
