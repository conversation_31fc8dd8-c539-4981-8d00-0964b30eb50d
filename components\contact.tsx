"use client"

import { useEffect, useRef, useState } from "react"
import dynamic from "next/dynamic"
import { gsap } from "gsap"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Mail, Phone, MapPin, Send } from "lucide-react"
import { useThemeColors } from "@/hooks/use-theme-colors"
import { useTheme } from "next-themes"

// Dynamically import Three.js components with SSR disabled
const Canvas = dynamic(() => import("@react-three/fiber").then((mod) => ({ default: mod.Canvas })), {
  ssr: false,
})
const Float = dynamic(() => import("@react-three/drei").then((mod) => ({ default: mod.Float })), {
  ssr: false,
})
const Tetrahedron = dynamic(() => import("@react-three/drei").then((mod) => ({ default: mod.Tetrahedron })), {
  ssr: false,
})
const Icosahedron = dynamic(() => import("@react-three/drei").then((mod) => ({ default: mod.Icosahedron })), {
  ssr: false,
})
const ContactPhysics = dynamic(() => import("@/components/particles/contact-physics").then((mod) => ({ default: mod.ContactPhysics })), {
  ssr: false,
})

function ContactShapes() {
  const { resolvedTheme } = useTheme()
  const colors = useThemeColors()

  return (
    <>
      <ContactPhysics count={250} radius={5} intensity={1.2} />

      <Float speed={1.5} rotationIntensity={0.3} floatIntensity={0.3} position={[-3, 2, 0]}>
        <Tetrahedron args={[0.4]}>
          <meshStandardMaterial color={colors.primary} />
        </Tetrahedron>
      </Float>
      <Float speed={1} rotationIntensity={0.2} floatIntensity={0.2} position={[3, -1, 0]}>
        <Icosahedron args={[0.3]}>
          <meshStandardMaterial color={colors.secondary} />
        </Icosahedron>
      </Float>
    </>
  )
}

export default function Contact() {
  const contactRef = useRef<HTMLDivElement>(null)
  const [mounted, setMounted] = useState(false)
  const { resolvedTheme } = useTheme()

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted || typeof window === "undefined") return

    const ctx = gsap.context(() => {
      gsap.fromTo(
        ".contact-content",
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power2.out",
          stagger: 0.2,
          scrollTrigger: {
            trigger: ".contact-section",
            start: "top 80%",
          },
        },
      )
    }, contactRef)

    return () => ctx.revert()
  }, [])

  const contactInfo = [
    {
      icon: Mail,
      title: "Email",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>",
    },
    {
      icon: Phone,
      title: "Phone",
      value: "+****************",
      href: "tel:+15551234567",
    },
    {
      icon: MapPin,
      title: "Location",
      value: "New York, NY",
      href: "#",
    },
  ]

  return (
    <section
      id="contact"
      ref={contactRef}
      className="py-20 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 relative overflow-hidden"
    >
      {/* 3D Background */}
      {mounted && (
        <div className="absolute inset-0 opacity-20">
          <Canvas camera={{ position: [0, 0, 5] }}>
            <ambientLight intensity={resolvedTheme === "dark" ? 0.4 : 0.5} />
            <pointLight position={[10, 10, 10]} intensity={resolvedTheme === "dark" ? 0.8 : 1.0} />
            <ContactShapes />
          </Canvas>
        </div>
      )}

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="contact-section space-y-16">
          <div className="text-center space-y-4 contact-content">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white">
              Ready To Get
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-purple-600 dark:from-orange-400 dark:to-purple-500">
                Started?
              </span>
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              You Know About Me, Let's Talk About You
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Info */}
            <div className="contact-content space-y-8">
              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <div key={index} className="flex items-center space-x-4 group">
                    <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-purple-600 dark:from-orange-400 dark:to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <info.icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 font-medium">{info.title}</div>
                      <a
                        href={info.href}
                        className="text-lg font-semibold text-gray-900 dark:text-white hover:text-orange-500 dark:hover:text-orange-400 transition-colors duration-200"
                      >
                        {info.value}
                      </a>
                    </div>
                  </div>
                ))}
              </div>

              <div className="bg-gradient-to-r from-orange-500 to-purple-600 dark:from-orange-400 dark:to-purple-500 rounded-2xl p-8 text-white">
                <h3 className="text-2xl font-bold mb-4">Let's work together!</h3>
                <p className="text-orange-100 dark:text-orange-50 mb-6">
                  I'm always interested in new opportunities and exciting projects. Let's discuss how we can bring your
                  ideas to life.
                </p>
                <Button className="bg-white text-orange-500 hover:bg-gray-100 dark:bg-gray-800 dark:text-orange-400 dark:hover:bg-gray-700">
                  <Send className="w-4 h-4 mr-2" />
                  SHOOT MESSAGE
                </Button>
              </div>
            </div>

            {/* Contact Form */}
            <Card className="contact-content border-0 shadow-2xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
              <CardContent className="p-8">
                <form className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">First Name</label>
                      <Input
                        placeholder="John"
                        className="border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 dark:bg-gray-700 dark:text-white"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Last Name</label>
                      <Input
                        placeholder="Doe"
                        className="border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 dark:bg-gray-700 dark:text-white"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      className="border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Subject</label>
                    <Input
                      placeholder="Project Discussion"
                      className="border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Message</label>
                    <Textarea
                      placeholder="Tell me about your project..."
                      rows={5}
                      className="border-gray-200 dark:border-gray-600 focus:border-orange-500 dark:focus:border-orange-400 resize-none dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <Button
                    type="submit"
                    size="lg"
                    className="w-full bg-gradient-to-r from-orange-500 to-purple-600 hover:from-orange-600 hover:to-purple-700 dark:from-orange-400 dark:to-purple-500 dark:hover:from-orange-500 dark:hover:to-purple-600 text-white"
                  >
                    <Send className="w-4 h-4 mr-2" />
                    Send Message
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="mt-20 border-t border-gray-200 dark:border-gray-700 pt-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">Portfolio</div>
            <div className="flex space-x-8">
              <a
                href="#home"
                className="text-gray-600 dark:text-gray-300 hover:text-orange-500 dark:hover:text-orange-400 transition-colors duration-200"
              >
                Home
              </a>
              <a
                href="#about"
                className="text-gray-600 dark:text-gray-300 hover:text-orange-500 dark:hover:text-orange-400 transition-colors duration-200"
              >
                About Us
              </a>
              <a
                href="#services"
                className="text-gray-600 dark:text-gray-300 hover:text-orange-500 dark:hover:text-orange-400 transition-colors duration-200"
              >
                Services
              </a>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700 text-center text-gray-500 dark:text-gray-400">
            <p>&copy; 2024 Portfolio. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </section>
  )
}
