"use client"

import { useRef, useMemo } from "react"
import { useFrame } from "@react-three/fiber"
import { Points, PointMaterial } from "@react-three/drei"
import { useTheme } from "next-themes"
import * as THREE from "three"

interface EnergyFieldProps {
  count?: number
  radius?: number
}

export function EnergyField({ count = 500, radius = 5 }: EnergyFieldProps) {
  const ref = useRef<THREE.Points>(null)
  const { resolvedTheme } = useTheme()
  const isDark = resolvedTheme === "dark"

  const [positions, phases] = useMemo(() => {
    const positions = new Float32Array(count * 3)
    const phases = new Float32Array(count)

    for (let i = 0; i < count; i++) {
      const i3 = i * 3

      if (isDark) {
        // Dark theme: Electric field pattern
        const angle1 = Math.random() * Math.PI * 2
        const angle2 = Math.random() * Math.PI
        const r = radius * Math.pow(Math.random(), 0.3)

        positions[i3] = r * Math.sin(angle2) * Math.cos(angle1)
        positions[i3 + 1] = r * Math.sin(angle2) * Math.sin(angle1)
        positions[i3 + 2] = r * Math.cos(angle2)
      } else {
        // Light theme: Gentle spiral pattern
        const angle = (i / count) * Math.PI * 4
        const r = (i / count) * radius
        const height = Math.sin(angle * 2) * 2

        positions[i3] = r * Math.cos(angle)
        positions[i3 + 1] = height
        positions[i3 + 2] = r * Math.sin(angle)
      }

      phases[i] = Math.random() * Math.PI * 2
    }

    return [positions, phases]
  }, [count, radius, isDark])

  useFrame((state) => {
    if (!ref.current) return

    const time = state.clock.getElapsedTime()
    const positions = ref.current.geometry.attributes.position.array as Float32Array

    for (let i = 0; i < count; i++) {
      const i3 = i * 3
      const phase = phases[i]

      if (isDark) {
        // Dark theme: Pulsing energy field
        const pulse = Math.sin(time * 3 + phase) * 0.3
        const originalR = Math.sqrt(
          positions[i3] * positions[i3] + positions[i3 + 1] * positions[i3 + 1] + positions[i3 + 2] * positions[i3 + 2],
        )

        if (originalR > 0) {
          const scale = 1 + pulse
          positions[i3] *= scale
          positions[i3 + 1] *= scale
          positions[i3 + 2] *= scale
        }
      } else {
        // Light theme: Gentle wave motion
        positions[i3 + 1] += Math.sin(time * 2 + phase) * 0.01
      }
    }

    ref.current.geometry.attributes.position.needsUpdate = true
  })

  return (
    <Points ref={ref} positions={positions}>
      <PointMaterial
        transparent
        size={isDark ? 0.04 : 0.025}
        color={isDark ? "#06b6d4" : "#f97316"}
        opacity={isDark ? 0.8 : 0.5}
        sizeAttenuation={true}
        depthWrite={false}
        blending={isDark ? THREE.AdditiveBlending : THREE.NormalBlending}
      />
    </Points>
  )
}
