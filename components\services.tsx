"use client"

import { useEffect, useRef } from "react"
import { Canvas } from "@react-three/fiber"
import { Float, Tor<PERSON>, <PERSON><PERSON> } from "@react-three/drei"
import { gsap } from "gsap"
import { But<PERSON> } from "@/components/ui/button"
import { SpotlightCard } from "@/components/ui/spotlight-card"
import { Code, Palette, Smartphone } from "lucide-react"
import { useThemeColors } from "@/hooks/use-theme-colors"
import { useTheme } from "next-themes"

function ServiceShapes() {
  const colors = useThemeColors()

  return (
    <>
      <Float speed={1} rotationIntensity={0.2} floatIntensity={0.2} position={[-3, 2, 0]}>
        <Torus args={[0.3, 0.1, 16, 32]}>
          <meshStandardMaterial color={colors.primary} />
        </Torus>
      </Float>
      <Float speed={1.5} rotationIntensity={0.3} floatIntensity={0.3} position={[3, -2, 0]}>
        <Cone args={[0.3, 0.6, 8]}>
          <meshStandardMaterial color={colors.secondary} />
        </Cone>
      </Float>
    </>
  )
}

export default function Services() {
  const servicesRef = useRef<HTMLDivElement>(null)
  const { resolvedTheme } = useTheme()

  useEffect(() => {
    if (typeof window === "undefined") return

    const ctx = gsap.context(() => {
      gsap.fromTo(
        ".service-card",
        { opacity: 0, y: 50, scale: 0.9 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8,
          ease: "power2.out",
          stagger: 0.2,
          scrollTrigger: {
            trigger: ".services-grid",
            start: "top 80%",
          },
        },
      )
    }, servicesRef)

    return () => ctx.revert()
  }, [])

  const services = [
    {
      icon: Code,
      title: "Developer",
      description:
        "Lorem ipsum dummy text are usually use in these section. Lorem ipsum dummy text are usually use in these section.",
      gradient: "from-blue-500 to-purple-600 dark:from-blue-400 dark:to-purple-500",
      spotlightColor: resolvedTheme === "dark" ? "rgba(96, 165, 250, 0.15)" : "rgba(59, 130, 246, 0.15)",
    },
    {
      icon: Smartphone,
      title: "UI/UX",
      description:
        "Lorem ipsum dummy text are usually use in these section. Lorem ipsum dummy text are usually use in these section.",
      gradient: "from-green-500 to-blue-600 dark:from-green-400 dark:to-blue-500",
      spotlightColor: resolvedTheme === "dark" ? "rgba(74, 222, 128, 0.15)" : "rgba(34, 197, 94, 0.15)",
    },
    {
      icon: Palette,
      title: "Design",
      description:
        "Lorem ipsum dummy text are usually use in these section. Lorem ipsum dummy text are usually use in these section.",
      gradient: "from-orange-500 to-red-600 dark:from-orange-400 dark:to-red-500",
      spotlightColor: resolvedTheme === "dark" ? "rgba(251, 146, 60, 0.15)" : "rgba(249, 115, 22, 0.15)",
    },
  ]

  return (
    <section
      id="services"
      ref={servicesRef}
      className="py-20 bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 relative overflow-hidden"
    >
      {/* 3D Background */}
      <div className="absolute inset-0 opacity-20">
        <Canvas camera={{ position: [0, 0, 5] }}>
          <ambientLight intensity={resolvedTheme === "dark" ? 0.4 : 0.5} />
          <pointLight position={[10, 10, 10]} intensity={resolvedTheme === "dark" ? 0.8 : 1.0} />
          <ServiceShapes />
        </Canvas>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-4 mb-16 animate-on-scroll">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white">
            My Awesome
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-purple-600 dark:from-orange-400 dark:to-purple-500">
              Services
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Lorem ipsum dummy text are usually use in these section. Lorem ipsum dummy text are usually use in these
            section.
          </p>
        </div>

        <div className="services-grid grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <SpotlightCard
              key={index}
              className="service-card group bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-0"
              spotlightColor={service.spotlightColor}
            >
              <div className="text-center space-y-6">
                <div
                  className={`w-16 h-16 mx-auto rounded-2xl bg-gradient-to-r ${service.gradient} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                >
                  <service.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{service.title}</h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">{service.description}</p>
              </div>
            </SpotlightCard>
          ))}
        </div>

        <div className="text-center mt-12 animate-on-scroll">
          <Button
            size="lg"
            className="bg-orange-500 hover:bg-orange-600 dark:bg-orange-600 dark:hover:bg-orange-700 text-white px-8 py-3"
          >
            Hire CV
          </Button>
        </div>
      </div>
    </section>
  )
}
