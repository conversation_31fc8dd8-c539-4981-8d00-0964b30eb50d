import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { ThemeTransition } from "@/components/theme-transition"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "3D Portfolio",
  description: "A modern 3D portfolio website with interactive elements",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
          <ThemeTransition />
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
