"use client"

import { useRef, useMemo } from "react"
import { useFrame } from "@react-three/fiber"
import { Points, PointMaterial } from "@react-three/drei"
import { useTheme } from "next-themes"
import * as THREE from "three"

interface ContactPhysicsProps {
  count?: number
  radius?: number
  intensity?: number
}

export function ContactPhysics({ count = 300, radius = 4, intensity = 1 }: ContactPhysicsProps) {
  const ref = useRef<THREE.Points>(null)
  const { resolvedTheme } = useTheme()
  const isDark = resolvedTheme === "dark"

  // Particle system with depth-based physics
  const particles = useMemo(() => {
    const temp = []
    const depthLayers = 3

    for (let i = 0; i < count; i++) {
      // Assign particles to depth layers
      const layer = Math.floor(i / (count / depthLayers))
      const depthZ = -(layer + 1) * 2 // Depth spacing

      // Create particles in a circular pattern with depth variation
      const angle = Math.random() * Math.PI * 2
      const r = Math.random() * (radius + layer * 0.5) // Larger radius for deeper layers

      // Position particles in a disc shape with depth
      const x = Math.cos(angle) * r
      const y = Math.sin(angle) * r
      const z = depthZ + (Math.random() - 0.5) * 1

      // Create particle with depth-based physics properties
      temp.push({
        position: new THREE.Vector3(x, y, z),
        velocity: new THREE.Vector3(0, 0, 0),
        acceleration: new THREE.Vector3(0, 0, 0),
        mass: 0.5 + Math.random() * 1.5,
        charge: Math.random() > 0.5 ? 1 : -1,
        size: 0.02 + Math.random() * 0.04 + layer * 0.01, // Larger particles in back
        originalPosition: new THREE.Vector3(x, y, z),
        collided: false,
        collisionTimer: 0,

        // Depth properties
        layer: layer,
        depth: Math.abs(depthZ),
        depthRatio: layer / (depthLayers - 1),

        // Depth-based interaction properties
        parallaxSensitivity: 1 - layer * 0.3, // Front layers more sensitive
        oscillationPhase: Math.random() * Math.PI * 2,
        oscillationFrequency: 0.5 + Math.random() * 1.0,
      })
    }
    return temp
  }, [count, radius])

  // Depth-aware collision detection
  const checkDepthCollisions = (particles: any[], delta: number) => {
    for (let i = 0; i < particles.length; i++) {
      const particle = particles[i]

      // Reset collision timer if it's active
      if (particle.collisionTimer > 0) {
        particle.collisionTimer -= delta
        if (particle.collisionTimer <= 0) {
          particle.collided = false
        }
      }

      for (let j = i + 1; j < particles.length; j++) {
        const otherParticle = particles[j]

        // Calculate 3D distance including depth
        const distance = particle.position.distanceTo(otherParticle.position)
        const depthDistance = Math.abs(particle.depth - otherParticle.depth)

        // Adjust collision threshold based on depth difference
        const collisionThreshold = 0.1 + depthDistance * 0.05

        // Check for collision
        if (distance < collisionThreshold) {
          // Mark particles as collided
          particle.collided = true
          otherParticle.collided = true
          particle.collisionTimer = 0.5
          otherParticle.collisionTimer = 0.5

          // Calculate collision response with depth consideration
          const normal = new THREE.Vector3().subVectors(otherParticle.position, particle.position).normalize()

          // Depth-based collision strength
          const depthFactor = 1 - depthDistance / 6 // Weaker collisions across depth
          const repulsionForce = 0.01 * intensity * depthFactor

          particle.velocity.sub(normal.clone().multiplyScalar(repulsionForce))
          otherParticle.velocity.add(normal.clone().multiplyScalar(repulsionForce))

          // Add depth-based energy with layer interaction
          const energyBoost = (isDark ? 0.005 : 0.003) * depthFactor
          const layerInteraction = particle.layer !== otherParticle.layer ? 1.5 : 1.0

          particle.velocity.add(
            new THREE.Vector3(
              (Math.random() - 0.5) * energyBoost * layerInteraction,
              (Math.random() - 0.5) * energyBoost * layerInteraction,
              (Math.random() - 0.5) * energyBoost * 0.5, // Less Z movement
            ),
          )

          // Cross-layer collision effects
          if (particle.layer !== otherParticle.layer) {
            // Particles can "push" between layers
            const depthPush = 0.02
            if (particle.layer < otherParticle.layer) {
              particle.position.z += depthPush
              otherParticle.position.z -= depthPush
            } else {
              particle.position.z -= depthPush
              otherParticle.position.z += depthPush
            }
          }
        }
      }
    }
  }

  useFrame((state, delta) => {
    if (!ref.current) return

    const time = state.clock.getElapsedTime()
    const positions = ref.current.geometry.attributes.position.array as Float32Array
    const colors = ref.current.geometry.attributes.color.array as Float32Array

    // Check for depth-aware collisions
    checkDepthCollisions(particles, delta)

    // Update particles with depth-based physics
    for (let i = 0; i < particles.length; i++) {
      const i3 = i * 3
      const particle = particles[i]

      // Apply depth-based forces
      // 1. Attraction to center with depth variation
      const toCenter = new THREE.Vector3().subVectors(
        new THREE.Vector3(0, 0, particle.originalPosition.z),
        particle.position,
      )
      const distanceToCenter = toCenter.length()
      const centerForce = toCenter
        .normalize()
        .multiplyScalar(0.0001 * distanceToCenter * distanceToCenter * (1 + particle.depthRatio))
      particle.acceleration.add(centerForce)

      // 2. Depth-based circular motion
      const circularStrength = 0.0001 * (1 - particle.depthRatio * 0.5) // Front layers rotate faster
      const perpendicular = new THREE.Vector3(-particle.position.y, particle.position.x, 0).normalize()
      perpendicular.multiplyScalar(circularStrength)
      particle.acceleration.add(perpendicular)

      // 3. Layer-specific oscillation
      const oscillation =
        Math.sin(time * particle.oscillationFrequency + particle.oscillationPhase) * 0.00005 * (1 + particle.depthRatio)
      particle.acceleration.add(
        new THREE.Vector3(
          Math.sin(particle.oscillationPhase) * oscillation,
          Math.cos(particle.oscillationPhase) * oscillation,
          Math.sin(time * 0.5 + particle.oscillationPhase) * oscillation * 0.5,
        ),
      )

      // 4. Depth-aware spring force to original position
      const toOriginal = new THREE.Vector3().subVectors(particle.originalPosition, particle.position)
      const springStrength = 0.001 * (1 + particle.depthRatio * 0.5) // Stronger for back layers
      toOriginal.multiplyScalar(springStrength)
      particle.acceleration.add(toOriginal)

      // 5. Inter-layer forces
      particles.forEach((otherParticle, j) => {
        if (i === j) return

        const layerDiff = Math.abs(particle.layer - otherParticle.layer)
        if (layerDiff === 1) {
          // Adjacent layers
          const distance = particle.position.distanceTo(otherParticle.position)
          if (distance < 1.5 && distance > 0.1) {
            const force = new THREE.Vector3()
              .subVectors(otherParticle.position, particle.position)
              .normalize()
              .multiplyScalar(0.000005 / (distance * distance))

            particle.acceleration.add(force) // Gentle attraction between layers
          }
        }
      })

      // Update velocity and position
      particle.velocity.add(particle.acceleration)
      particle.velocity.multiplyScalar(0.98) // Damping
      particle.position.add(particle.velocity)

      // Depth boundaries
      const minZ = particle.originalPosition.z - 1
      const maxZ = particle.originalPosition.z + 1
      if (particle.position.z < minZ) {
        particle.position.z = minZ
        particle.velocity.z *= -0.5
      }
      if (particle.position.z > maxZ) {
        particle.position.z = maxZ
        particle.velocity.z *= -0.5
      }

      // Reset acceleration
      particle.acceleration.set(0, 0, 0)

      // Update positions array
      positions[i3] = particle.position.x
      positions[i3 + 1] = particle.position.y
      positions[i3 + 2] = particle.position.z

      // Update colors based on depth, collision state, and velocity
      const speed = particle.velocity.length() * 100
      const speedFactor = Math.min(speed, 1)
      const depthFactor = 1 - particle.depth / 6 // Fade with depth
      const layerHue = particle.layer * 0.2 // Different hues per layer

      if (particle.collided) {
        // Collision highlight colors with depth
        if (isDark) {
          colors[i3] = 1.0 * depthFactor + speedFactor * 0.5 // R
          colors[i3 + 1] = (0.5 + speedFactor * 0.5) * depthFactor // G
          colors[i3 + 2] = 0.2 * depthFactor // B
        } else {
          colors[i3] = 1.0 * depthFactor + speedFactor * 0.3 // R
          colors[i3 + 1] = (0.3 + speedFactor * 0.3) * depthFactor // G
          colors[i3 + 2] = (0.8 + speedFactor * 0.2) * depthFactor // B
        }
      } else {
        // Normal depth-based colors
        if (isDark) {
          colors[i3] = (0.2 + speedFactor * 0.8 + layerHue * 0.3) * depthFactor // R
          colors[i3 + 1] = (0.5 + speedFactor * 0.5 + layerHue * 0.2) * depthFactor // G
          colors[i3 + 2] = (1.0 - layerHue * 0.3) * depthFactor // B
        } else {
          colors[i3] = (1.0 - layerHue * 0.2) * depthFactor // R
          colors[i3 + 1] = (0.5 + speedFactor * 0.3 + layerHue * 0.2) * depthFactor // G
          colors[i3 + 2] = (0.2 + speedFactor * 0.2 + layerHue * 0.4) * depthFactor // B
        }
      }
    }

    ref.current.geometry.attributes.position.needsUpdate = true
    ref.current.geometry.attributes.color.needsUpdate = true

    // Rotate the entire system slowly with depth-based variation
    ref.current.rotation.z += delta * 0.05
    ref.current.rotation.x += delta * 0.01
  })

  // Initial positions and colors
  const [positions, colors] = useMemo(() => {
    const positions = new Float32Array(count * 3)
    const colors = new Float32Array(count * 3)

    for (let i = 0; i < count; i++) {
      const i3 = i * 3
      const p = particles[i]

      positions[i3] = p.position.x
      positions[i3 + 1] = p.position.y
      positions[i3 + 2] = p.position.z

      const depthFactor = 1 - p.depth / 6
      const layerHue = p.layer * 0.2

      if (isDark) {
        colors[i3] = (0.2 + layerHue * 0.3) * depthFactor // R
        colors[i3 + 1] = (0.5 + layerHue * 0.2) * depthFactor // G
        colors[i3 + 2] = (1.0 - layerHue * 0.3) * depthFactor // B
      } else {
        colors[i3] = (1.0 - layerHue * 0.2) * depthFactor // R
        colors[i3 + 1] = (0.5 + layerHue * 0.2) * depthFactor // G
        colors[i3 + 2] = (0.2 + layerHue * 0.4) * depthFactor // B
      }
    }

    return [positions, colors]
  }, [particles, count, isDark])

  return (
    <Points ref={ref} positions={positions} colors={colors}>
      <PointMaterial
        transparent
        size={isDark ? 0.05 : 0.04}
        sizeAttenuation={true}
        depthWrite={false}
        blending={isDark ? THREE.AdditiveBlending : THREE.NormalBlending}
        vertexColors
        opacity={isDark ? 0.8 : 0.6}
      />
    </Points>
  )
}
