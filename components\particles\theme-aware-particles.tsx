"use client"

import { useRef, useMemo, useEffect, useState } from "react"
import { useFrame } from "@react-three/fiber"
import { Points, PointMaterial } from "@react-three/drei"
import { useTheme } from "next-themes"
import * as THREE from "three"

interface ThemeAwareParticlesProps {
  count?: number
  themeChangeSpeed?: number
}

export function ThemeAwareParticles({ count = 200, themeChangeSpeed = 2.0 }: ThemeAwareParticlesProps) {
  const ref = useRef<THREE.Points>(null)
  const { resolvedTheme } = useTheme()
  const isDark = resolvedTheme === "dark"
  const [transitionProgress, setTransitionProgress] = useState(isDark ? 1 : 0)
  const [prevTheme, setPrevTheme] = useState(isDark)

  // Track theme changes for smooth transitions
  useEffect(() => {
    if (prevTheme !== isDark) {
      setPrevTheme(isDark)
    }
  }, [isDark, prevTheme])

  // Particle system with theme transition properties
  const particles = useMemo(() => {
    const temp = []
    for (let i = 0; i < count; i++) {
      // Create particles in a spherical distribution
      const theta = Math.random() * Math.PI * 2
      const phi = Math.acos(2 * Math.random() - 1)
      const radius = 2 + Math.random() * 3

      const x = radius * Math.sin(phi) * Math.cos(theta)
      const y = radius * Math.sin(phi) * Math.sin(theta)
      const z = radius * Math.cos(phi)

      // Theme-specific properties
      const lightProperties = {
        color: new THREE.Color().setHSL(
          0.05 + Math.random() * 0.05, // Orange hue
          0.7 + Math.random() * 0.3,
          0.5 + Math.random() * 0.3,
        ),
        size: 0.03 + Math.random() * 0.02,
        speed: 0.2 + Math.random() * 0.3,
        rotationFactor: 0.2 + Math.random() * 0.3,
        waveAmplitude: 0.1 + Math.random() * 0.2,
      }

      const darkProperties = {
        color: new THREE.Color().setHSL(
          0.6 + Math.random() * 0.1, // Blue hue
          0.7 + Math.random() * 0.3,
          0.5 + Math.random() * 0.3,
        ),
        size: 0.04 + Math.random() * 0.03,
        speed: 0.1 + Math.random() * 0.2,
        rotationFactor: 0.1 + Math.random() * 0.2,
        waveAmplitude: 0.2 + Math.random() * 0.3,
      }

      temp.push({
        position: new THREE.Vector3(x, y, z),
        velocity: new THREE.Vector3(
          (Math.random() - 0.5) * 0.01,
          (Math.random() - 0.5) * 0.01,
          (Math.random() - 0.5) * 0.01,
        ),
        originalPosition: new THREE.Vector3(x, y, z),
        light: lightProperties,
        dark: darkProperties,
        phase: Math.random() * Math.PI * 2,
        frequency: 0.5 + Math.random() * 1.0,
      })
    }
    return temp
  }, [count])

  // Animate theme transition
  useFrame((state, delta) => {
    if (!ref.current) return

    // Smoothly transition between themes
    const targetProgress = isDark ? 1 : 0
    setTransitionProgress((prev) => {
      const step = delta * themeChangeSpeed
      if (Math.abs(targetProgress - prev) < step) return targetProgress
      return prev + (targetProgress > prev ? step : -step)
    })

    const time = state.clock.getElapsedTime()
    const positions = ref.current.geometry.attributes.position.array as Float32Array
    const colors = ref.current.geometry.attributes.color.array as Float32Array
    const sizes = ref.current.geometry.attributes.size?.array as Float32Array

    // Update particles with theme-aware properties
    particles.forEach((particle, i) => {
      const i3 = i * 3

      // Interpolate between light and dark theme properties
      const color = new THREE.Color()
      color.r = THREE.MathUtils.lerp(particle.light.color.r, particle.dark.color.r, transitionProgress)
      color.g = THREE.MathUtils.lerp(particle.light.color.g, particle.dark.color.g, transitionProgress)
      color.b = THREE.MathUtils.lerp(particle.light.color.b, particle.dark.color.b, transitionProgress)

      const size = THREE.MathUtils.lerp(particle.light.size, particle.dark.size, transitionProgress)
      const speed = THREE.MathUtils.lerp(particle.light.speed, particle.dark.speed, transitionProgress)
      const rotationFactor = THREE.MathUtils.lerp(
        particle.light.rotationFactor,
        particle.dark.rotationFactor,
        transitionProgress,
      )
      const waveAmplitude = THREE.MathUtils.lerp(
        particle.light.waveAmplitude,
        particle.dark.waveAmplitude,
        transitionProgress,
      )

      // Theme-specific motion patterns
      if (isDark) {
        // Dark theme: Cosmic, flowing motion
        const angle = time * speed * 0.3
        const radius = 3 + Math.sin(time * 0.2 + particle.phase) * waveAmplitude

        // Spiral galaxy-like motion
        particle.position.x =
          Math.cos(angle + particle.phase * 10) * radius * (1 + Math.sin(time * 0.1) * 0.1) * rotationFactor +
          particle.originalPosition.x * (1 - rotationFactor)
        particle.position.y =
          Math.sin(angle + particle.phase * 10) * radius * (1 + Math.cos(time * 0.1) * 0.1) * rotationFactor +
          particle.originalPosition.y * (1 - rotationFactor)
        particle.position.z =
          Math.sin(time * 0.2 + particle.phase) * waveAmplitude +
          particle.originalPosition.z * (1 - rotationFactor * 0.5)
      } else {
        // Light theme: Gentle, floating motion
        const waveX = Math.sin(time * 0.5 + particle.phase) * waveAmplitude
        const waveY = Math.cos(time * 0.4 + particle.phase) * waveAmplitude
        const waveZ = Math.sin(time * 0.3 + particle.phase * 2) * waveAmplitude * 0.5

        // Soft, floating motion
        particle.position.x = particle.originalPosition.x + waveX
        particle.position.y = particle.originalPosition.y + waveY
        particle.position.z = particle.originalPosition.z + waveZ
      }

      // Update positions
      positions[i3] = particle.position.x
      positions[i3 + 1] = particle.position.y
      positions[i3 + 2] = particle.position.z

      // Update colors with theme transition
      colors[i3] = color.r
      colors[i3 + 1] = color.g
      colors[i3 + 2] = color.b

      // Update sizes
      if (sizes) {
        sizes[i] = size * (1 + Math.sin(time * 2 + particle.phase) * 0.2) // Pulsing effect
      }
    })

    // Update geometry
    ref.current.geometry.attributes.position.needsUpdate = true
    ref.current.geometry.attributes.color.needsUpdate = true
    if (ref.current.geometry.attributes.size) {
      ref.current.geometry.attributes.size.needsUpdate = true
    }

    // Rotate the entire system based on theme
    const rotationSpeed = THREE.MathUtils.lerp(0.05, 0.1, transitionProgress)
    ref.current.rotation.y += delta * rotationSpeed
    ref.current.rotation.z += delta * rotationSpeed * 0.3
  })

  // Initial geometry setup
  const [positions, colors, sizes] = useMemo(() => {
    const positions = new Float32Array(count * 3)
    const colors = new Float32Array(count * 3)
    const sizes = new Float32Array(count)

    particles.forEach((particle, i) => {
      const i3 = i * 3

      // Initial positions
      positions[i3] = particle.position.x
      positions[i3 + 1] = particle.position.y
      positions[i3 + 2] = particle.position.z

      // Initial colors (based on current theme)
      const initialColor = isDark ? particle.dark.color : particle.light.color
      colors[i3] = initialColor.r
      colors[i3 + 1] = initialColor.g
      colors[i3 + 2] = initialColor.b

      // Initial sizes
      sizes[i] = isDark ? particle.dark.size : particle.light.size
    })

    return [positions, colors, sizes]
  }, [particles, count, isDark])

  return (
    <Points ref={ref} positions={positions} colors={colors}>
      <bufferGeometry>
        <bufferAttribute attach="attributes-position" count={count} array={positions} itemSize={3} />
        <bufferAttribute attach="attributes-color" count={count} array={colors} itemSize={3} />
        <bufferAttribute attach="attributes-size" count={count} array={sizes} itemSize={1} />
      </bufferGeometry>
      <PointMaterial
        transparent
        vertexColors
        sizeAttenuation={true}
        depthWrite={false}
        blending={isDark ? THREE.AdditiveBlending : THREE.NormalBlending}
        opacity={0.8}
      />
    </Points>
  )
}
