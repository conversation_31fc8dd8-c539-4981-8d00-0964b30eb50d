"use client"

import { useState, useEffect } from "react"
import { Menu, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { ThemeToggle } from "@/components/theme-toggle"

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const navItems = [
    { name: "Home", href: "#home" },
    { name: "About", href: "#about" },
    { name: "Services", href: "#services" },
    { name: "Projects", href: "#projects" },
    { name: "Contact", href: "#contact" },
  ]

  return (
    <nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled ? "bg-white/90 dark:bg-gray-900/90 backdrop-blur-md shadow-lg" : "bg-transparent"
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div className="text-2xl font-bold text-gray-900 dark:text-white">Portfolio</div>

          {/* Desktop Navigation with Gooey Effect */}
          <div className="hidden md:flex relative">
            <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-full p-1 relative">
              {/* Gooey background blob */}
              <div
                className="absolute top-1 bottom-1 bg-orange-500 dark:bg-orange-400 rounded-full transition-all duration-300 ease-out"
                style={{
                  width: "80px",
                  left: "4px",
                  transform: "translateX(0px)",
                }}
                id="gooey-blob"
              />

              {navItems.map((item, index) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="relative z-10 px-4 py-2 text-sm font-medium transition-colors duration-200 rounded-full text-gray-700 dark:text-gray-300 hover:text-white dark:hover:text-white"
                  onMouseEnter={(e) => {
                    const blob = document.getElementById("gooey-blob")
                    if (blob) {
                      const rect = e.currentTarget.getBoundingClientRect()
                      const parent = e.currentTarget.parentElement?.getBoundingClientRect()
                      if (parent) {
                        blob.style.transform = `translateX(${rect.left - parent.left - 4}px)`
                        blob.style.width = `${rect.width}px`
                      }
                    }
                  }}
                >
                  {item.name}
                </a>
              ))}
            </div>
          </div>

          <div className="hidden md:flex items-center space-x-4">
            <ThemeToggle />
            <Button className="bg-orange-500 hover:bg-orange-600 dark:bg-orange-600 dark:hover:bg-orange-700 text-white">
              Contact Me
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-2">
            <ThemeToggle />
            <Button variant="ghost" size="icon" onClick={() => setIsOpen(!isOpen)} className="dark:text-white">
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white dark:bg-gray-900 rounded-lg shadow-lg border dark:border-gray-700">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="block px-3 py-2 text-gray-700 dark:text-gray-300 hover:text-orange-500 dark:hover:text-orange-400 transition-colors duration-200"
                  onClick={() => setIsOpen(false)}
                >
                  {item.name}
                </a>
              ))}
              <Button className="w-full mt-4 bg-orange-500 hover:bg-orange-600 dark:bg-orange-600 dark:hover:bg-orange-700 text-white">
                Contact Me
              </Button>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
