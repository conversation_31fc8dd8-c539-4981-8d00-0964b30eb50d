"use client"

import { useRef, useMemo } from "react"
import { useFrame } from "@react-three/fiber"
import { Sphere } from "@react-three/drei"
import { useTheme } from "next-themes"
import * as THREE from "three"

interface CollisionSpheresProps {
  count?: number
  bounds?: number
}

export function CollisionSpheres({ count = 5, bounds = 8 }: CollisionSpheresProps) {
  const groupRef = useRef<THREE.Group>(null)
  const { resolvedTheme } = useTheme()
  const isDark = resolvedTheme === "dark"

  // Sphere physics data
  const spheres = useMemo(() => {
    return Array.from({ length: count }, () => ({
      position: new THREE.Vector3(
        (Math.random() - 0.5) * bounds,
        (Math.random() - 0.5) * bounds,
        (Math.random() - 0.5) * bounds,
      ),
      velocity: new THREE.Vector3(
        (Math.random() - 0.5) * 0.02,
        (Math.random() - 0.5) * 0.02,
        (Math.random() - 0.5) * 0.02,
      ),
      radius: 0.2 + Math.random() * 0.3,
      mass: 1 + Math.random() * 2,
      color: new THREE.Color().setHSL(Math.random(), 0.7, isDark ? 0.6 : 0.5),
    }))
  }, [count, bounds, isDark])

  useFrame((state, delta) => {
    if (!groupRef.current) return

    const deltaTime = delta * 60

    // Update sphere physics
    spheres.forEach((sphere, i) => {
      // Apply gravity
      sphere.velocity.y -= 0.001 * deltaTime

      // Update position
      sphere.position.add(sphere.velocity.clone().multiplyScalar(deltaTime))

      // Boundary collisions
      const halfBounds = bounds / 2
      if (sphere.position.x + sphere.radius > halfBounds || sphere.position.x - sphere.radius < -halfBounds) {
        sphere.velocity.x *= -0.8
        sphere.position.x = Math.max(
          -halfBounds + sphere.radius,
          Math.min(halfBounds - sphere.radius, sphere.position.x),
        )
      }

      if (sphere.position.y + sphere.radius > halfBounds || sphere.position.y - sphere.radius < -halfBounds) {
        sphere.velocity.y *= -0.8
        sphere.position.y = Math.max(
          -halfBounds + sphere.radius,
          Math.min(halfBounds - sphere.radius, sphere.position.y),
        )
      }

      if (sphere.position.z + sphere.radius > halfBounds || sphere.position.z - sphere.radius < -halfBounds) {
        sphere.velocity.z *= -0.8
        sphere.position.z = Math.max(
          -halfBounds + sphere.radius,
          Math.min(halfBounds - sphere.radius, sphere.position.z),
        )
      }

      // Sphere-to-sphere collisions
      spheres.forEach((otherSphere, j) => {
        if (i >= j) return

        const distance = sphere.position.distanceTo(otherSphere.position)
        const minDistance = sphere.radius + otherSphere.radius

        if (distance < minDistance) {
          // Calculate collision normal
          const normal = new THREE.Vector3().subVectors(otherSphere.position, sphere.position).normalize()

          // Separate spheres
          const overlap = minDistance - distance
          const separation = normal.clone().multiplyScalar(overlap * 0.5)

          sphere.position.sub(separation)
          otherSphere.position.add(separation)

          // Calculate relative velocity
          const relativeVelocity = new THREE.Vector3().subVectors(sphere.velocity, otherSphere.velocity)
          const velocityAlongNormal = relativeVelocity.dot(normal)

          if (velocityAlongNormal > 0) return

          // Calculate collision response
          const restitution = 0.7
          const impulseScalar = -(1 + restitution) * velocityAlongNormal
          const totalMass = sphere.mass + otherSphere.mass
          const impulse = normal.clone().multiplyScalar(impulseScalar / totalMass)

          sphere.velocity.add(impulse.clone().multiplyScalar(otherSphere.mass))
          otherSphere.velocity.sub(impulse.clone().multiplyScalar(sphere.mass))
        }
      })

      // Update mesh position
      const mesh = groupRef.current?.children[i] as THREE.Mesh
      if (mesh) {
        mesh.position.copy(sphere.position)
      }
    })
  })

  return (
    <group ref={groupRef}>
      {spheres.map((sphere, i) => (
        <Sphere key={i} args={[sphere.radius, 16, 16]} position={sphere.position.toArray()}>
          <meshStandardMaterial
            color={sphere.color}
            transparent
            opacity={isDark ? 0.3 : 0.2}
            emissive={isDark ? sphere.color.clone().multiplyScalar(0.2) : new THREE.Color(0x000000)}
          />
        </Sphere>
      ))}
    </group>
  )
}
