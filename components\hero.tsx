"use client"

import { useEffect, useRef, useState } from "react"
import { Canvas } from "@react-three/fiber"
import { Float, Center, OrbitControls } from "@react-three/drei"
import { gsap } from "gsap"
import { Button } from "@/components/ui/button"
import { ProfileCard } from "@/components/ui/profile-card"
import { useThemeColors } from "@/hooks/use-theme-colors"
import { useTheme } from "next-themes"
import { ThemeParticles } from "@/components/particles/theme-particles"

function FloatingText() {
  const colors = useThemeColors()

  return (
    <Float speed={2} rotationIntensity={0.5} floatIntensity={0.5}>
      <Center>
        <mesh>
          <boxGeometry args={[1, 1, 0.2]} />
          <meshStandardMaterial color={colors.primary} />
        </mesh>
      </Center>
    </Float>
  )
}

export default function Hero() {
  const heroRef = useRef<HTMLDivElement>(null)
  const [mounted, setMounted] = useState(false)
  const { resolvedTheme } = useTheme()

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted || typeof window === "undefined" || !heroRef.current) return

    const ctx = gsap.context(() => {
      const tl = gsap.timeline()

      const heroTitle = heroRef.current?.querySelector(".hero-title")
      const heroSubtitle = heroRef.current?.querySelector(".hero-subtitle")
      const heroCta = heroRef.current?.querySelector(".hero-cta")
      const heroProfile = heroRef.current?.querySelector(".hero-profile")

      if (heroTitle) {
        tl.fromTo(heroTitle, { opacity: 0, y: 100 }, { opacity: 1, y: 0, duration: 1, ease: "power3.out" })
      }

      if (heroSubtitle) {
        tl.fromTo(heroSubtitle, { opacity: 0, y: 50 }, { opacity: 1, y: 0, duration: 0.8, ease: "power2.out" }, "-=0.5")
      }

      if (heroCta) {
        tl.fromTo(
          heroCta,
          { opacity: 0, scale: 0.8 },
          { opacity: 1, scale: 1, duration: 0.6, ease: "back.out(1.7)" },
          "-=0.3",
        )
      }

      if (heroProfile) {
        tl.fromTo(heroProfile, { opacity: 0, x: 100 }, { opacity: 1, x: 0, duration: 1, ease: "power2.out" }, "-=0.8")
      }
    }, heroRef)

    return () => ctx.revert()
  }, [mounted])

  if (!mounted) {
    return (
      <section className="relative min-h-screen flex items-center overflow-hidden bg-gradient-to-br from-orange-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse"></div>
              </div>
              <div className="flex gap-4">
                <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse"></div>
                <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse"></div>
              </div>
            </div>
            <div className="h-96 bg-gray-200 dark:bg-gray-700 rounded-3xl animate-pulse"></div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section
      id="home"
      ref={heroRef}
      className="relative min-h-screen flex items-center overflow-hidden bg-gradient-to-br from-orange-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900"
    >
      {/* 3D Background with Theme Particles */}
      <div className="absolute inset-0 opacity-30 dark:opacity-20">
        <Canvas camera={{ position: [0, 0, 5], fov: 75 }}>
          <ambientLight intensity={resolvedTheme === "dark" ? 0.4 : 0.5} />
          <pointLight position={[10, 10, 10]} intensity={resolvedTheme === "dark" ? 0.8 : 1.0} />

          {/* Hero-specific particles */}
          <ThemeParticles count={400} speed={0.2} size={0.015} />

          <FloatingText />
          <OrbitControls enableZoom={false} enablePan={false} autoRotate autoRotateSpeed={0.5} />
        </Canvas>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="hero-title text-5xl lg:text-7xl font-bold text-gray-900 dark:text-white leading-tight">
                Hi! I Am
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-purple-600 dark:from-orange-400 dark:to-purple-500">
                  Creative Dev
                </span>
              </h1>
              <p className="hero-subtitle text-xl text-gray-600 dark:text-gray-300 max-w-lg">
                Product designer and digital creative director working in design field for 5+ years for specialist user
                interface design.
              </p>
            </div>

            <div className="hero-cta flex flex-col sm:flex-row gap-4">
              <Button
                size="lg"
                className="bg-orange-500 hover:bg-orange-600 dark:bg-orange-600 dark:hover:bg-orange-700 text-white px-8 py-3"
              >
                Hire Me
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-gray-300 dark:border-gray-600 hover:border-orange-500 dark:hover:border-orange-400 dark:text-white px-8 py-3"
              >
                View Portfolio
              </Button>
            </div>

            <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
              <span>Work For All This Brand & Client</span>
              <div className="flex space-x-4">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
              </div>
            </div>
          </div>

          <div className="hero-profile">
            <ProfileCard name="Alex Johnson" title="Software Engineer" username="@alexcodes" isOnline={true} />
          </div>
        </div>
      </div>
    </section>
  )
}
